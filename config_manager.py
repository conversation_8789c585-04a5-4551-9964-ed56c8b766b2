"""
配置管理模块
负责应用程序的所有配置相关操作，包括加载、保存、颜色预设管理等
"""

import json
from pathlib import Path


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, app_instance):
        """初始化配置管理器

        Args:
            app_instance: 主应用实例的引用
        """
        self.app = app_instance

        # 管理配置文件路径
        from pathlib import Path
        current_dir = Path.cwd()
        self.config_file = current_dir / "config.json"

        # 为了向后兼容，也设置到app实例上
        self.app.config_file = self.config_file

        # 默认黄字颜色预设
        self.yellow_text_presets = {
            "默认": {"r": 174, "g": 159, "b": 112},
            "纯黄": {"r": 255, "g": 255, "b": 0},
            "秋麒麟": {"r": 218, "g": 165, "b": 32},
            "晒黑": {"r": 210, "g": 180, "b": 140},
            "结实的树": {"r": 222, "g": 184, "b": 135},
            "马鞍棕色": {"r": 139, "g": 69, "b": 19},
            "沙棕色": {"r": 244, "g": 164, "b": 96},
        }
        
        # 初始化当前黄字颜色
        self.current_yellow_color_name = None
        self.yellow_text_rgb = None
        
        # 设置默认颜色
        self._set_default_yellow_color()

        # 为了向后兼容，将颜色预设也设置到app实例上
        self.app.yellow_text_presets = self.yellow_text_presets
    
    def _set_default_yellow_color(self):
        """设置默认黄字颜色"""
        if not self.current_yellow_color_name:
            self.current_yellow_color_name = list(self.yellow_text_presets.keys())[0]
            self.yellow_text_rgb = dict(self.yellow_text_presets[self.current_yellow_color_name])

            # 同步到app实例（向后兼容）
            self.app.current_yellow_color_name = self.current_yellow_color_name
            self.app.yellow_text_rgb = self.yellow_text_rgb
    
    def load_config(self):
        """加载配置文件"""
        try:
            if self.config_file.exists():
                with open(self.config_file, "r", encoding="utf-8") as f:
                    config = json.load(f)
                    
                    # 加载基本配置
                    self.app.zoom_ratio = config.get("zoom_ratio", 1.0)
                    self.app.zoom_step = config.get("zoom_step", 1.1)
                    self.app.original_display_mode = config.get("original_display_mode", "stretch")

                    # 延迟更新UI（需要等UI组件创建完成）
                    self.app.root.after(100, self._update_original_ui)
                    
                    # 加载自定义黄字颜色
                    self._load_custom_colors(config)
                    
                    # 设置当前颜色
                    self._load_current_color(config)
                    
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            self._set_default_config()
    
    def _update_original_ui(self):
        """更新原图按钮文本和菜单状态"""
        if hasattr(self.app, 'btn_original'):
            if hasattr(self.app, 'original_mode') and self.app.original_mode:
                self.app.btn_original.config(text="原图")
            else:
                self.app.btn_original.config(text="原图")

        # 更新画布菜单状态（如果已创建）
        if hasattr(self.app, 'original_display_var'):
            if hasattr(self.app, 'original_mode') and self.app.original_mode:
                self.app.original_display_var.set(self.app.original_display_mode)
            else:
                self.app.original_display_var.set("stretch")  # 默认拉伸模式
    
    def _load_custom_colors(self, config):
        """加载自定义颜色"""
        custom_colors_loaded = False
        for item in config.get("custom_yellow_colors", []):
            if item["name"] not in self.yellow_text_presets:
                self.yellow_text_presets[item["name"]] = {
                    "r": item["r"],
                    "g": item["g"],
                    "b": item["b"],
                }
                custom_colors_loaded = True
                print(f"加载自定义颜色: {item['name']} RGB({item['r']}, {item['g']}, {item['b']})")

        # 如果加载了自定义颜色，更新菜单
        if custom_colors_loaded and hasattr(self.app, 'ui_components'):
            self.app.root.after(200, lambda: self.app.ui_components.update_yellow_color_menu())
    
    def _load_current_color(self, config):
        """加载当前选中的颜色"""
        saved_color_name = config.get("yellow_color_name")
        if saved_color_name and saved_color_name in self.yellow_text_presets:
            self.current_yellow_color_name = saved_color_name
            self.yellow_text_rgb = dict(self.yellow_text_presets[saved_color_name])
        else:
            self._set_default_yellow_color()
    
    def _set_default_config(self):
        """设置默认配置"""
        self.app.zoom_ratio = 1.0
        self.app.zoom_step = 1.1
        self._set_default_yellow_color()
    
    def save_config(self):
        """保存配置文件"""
        try:
            config = {
                "zoom_ratio": self.app.zoom_ratio,
                "zoom_step": self.app.zoom_step,
                "yellow_color_name": self.current_yellow_color_name,
                "original_display_mode": self.app.original_display_mode,
            }
            
            # 收集自定义颜色（排除预设）
            config["custom_yellow_colors"] = self._get_custom_colors()
            
            with open(self.config_file, "w", encoding="utf-8") as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def _get_custom_colors(self):
        """获取自定义颜色列表"""
        preset_names = set([
            "默认", "纯黄", "秋麒麟", "晒黑", "结实的树", "马鞍棕色", "沙棕色"
        ])
        
        custom_colors = [
            {"name": name, "r": rgb["r"], "g": rgb["g"], "b": rgb["b"]}
            for name, rgb in self.yellow_text_presets.items()
            if name not in preset_names
        ]
        
        return custom_colors
    
    def add_custom_color(self, name, r, g, b):
        """添加自定义颜色
        
        Args:
            name: 颜色名称
            r, g, b: RGB值
        """
        self.yellow_text_presets[name] = {"r": r, "g": g, "b": b}
        print(f"添加自定义颜色: {name} RGB({r}, {g}, {b})")
    
    def remove_custom_color(self, name):
        """移除自定义颜色
        
        Args:
            name: 颜色名称
        """
        preset_names = set([
            "默认", "纯黄", "秋麒麟", "晒黑", "结实的树", "马鞍棕色", "沙棕色"
        ])
        
        if name not in preset_names and name in self.yellow_text_presets:
            del self.yellow_text_presets[name]
            print(f"移除自定义颜色: {name}")
            
            # 如果删除的是当前颜色，重置为默认
            if self.current_yellow_color_name == name:
                self._set_default_yellow_color()
    
    def set_current_color(self, color_name):
        """设置当前颜色

        Args:
            color_name: 颜色名称
        """
        if color_name in self.yellow_text_presets:
            self.current_yellow_color_name = color_name
            self.yellow_text_rgb = dict(self.yellow_text_presets[color_name])

            # 同步到app实例（向后兼容）
            self.app.current_yellow_color_name = self.current_yellow_color_name
            self.app.yellow_text_rgb = self.yellow_text_rgb

            print(f"设置当前颜色: {color_name}")
    
    def get_color_rgb(self, color_name=None):
        """获取颜色的RGB值
        
        Args:
            color_name: 颜色名称，如果为None则返回当前颜色
            
        Returns:
            dict: RGB值字典
        """
        if color_name is None:
            return self.yellow_text_rgb
        
        return self.yellow_text_presets.get(color_name, self.yellow_text_rgb)
    
    def get_all_color_names(self):
        """获取所有颜色名称列表

        Returns:
            list: 颜色名称列表
        """
        return list(self.yellow_text_presets.keys())

    def set_yellow_text_color(self, name):
        """切换黄字颜色并刷新图片"""
        if name in self.yellow_text_presets:
            self.current_yellow_color_name = name
            self.yellow_text_rgb = dict(self.yellow_text_presets[name])

            # 同步到app实例（向后兼容）
            self.app.current_yellow_color_name = self.current_yellow_color_name
            self.app.yellow_text_rgb = self.yellow_text_rgb

            # 显示切换的颜色信息
            rgb = self.yellow_text_rgb
            print(f"切换黄字颜色: {name} (R={rgb['r']}, G={rgb['g']}, B={rgb['b']})")

            # 清空缓存并更新图片
            if hasattr(self.app, "image_cache"):
                self.app.image_cache.clear()  # 清空所有缩放缓存

            self.app.update_image()
            if hasattr(self.app, 'second_window') and self.app.second_window and hasattr(self.app, 'sync_enabled') and self.app.sync_enabled:
                self.app.update_projection()

            # 保存配置
            self.save_config()

    def apply_color_from_picker(self, r, g, b, window=None):
        """从颜色选择器应用颜色"""
        # 直接应用颜色，不保存为预设
        self.yellow_text_rgb = {"r": r, "g": g, "b": b}

        # 同步到app实例（向后兼容）
        self.app.yellow_text_rgb = self.yellow_text_rgb

        # 更新图片显示
        if hasattr(self.app, 'image') and self.app.image:
            self.app.update_image()
        if hasattr(self.app, 'second_window') and self.app.second_window and hasattr(self.app, 'sync_enabled') and self.app.sync_enabled:
            self.app.update_projection()

        # 保存配置
        self.save_config()

        # 关闭窗口（如果提供了窗口参数）
        if window:
            window.destroy()

        print(f"应用黄字颜色: RGB({r}, {g}, {b})")

    def save_custom_yellow_color(self, name, r, g, b):
        """保存自定义颜色"""
        # 添加到颜色预设中
        self.yellow_text_presets[name] = {"r": r, "g": g, "b": b}

        # 同步到app实例
        self.app.yellow_text_presets = self.yellow_text_presets

        # 保存配置文件
        self.save_config()
        print(f"保存自定义颜色: {name} RGB({r}, {g}, {b})")

    def open_color_picker_window(self):
        """打开颜色选择器窗口"""
        import tkinter as tk
        from tkinter import ttk, messagebox

        # 创建颜色选择器窗口
        color_window = tk.Toplevel(self.app.root)
        color_window.title("黄字颜色选择器")
        color_window.geometry("500x600")
        color_window.resizable(False, False)
        color_window.transient(self.app.root)
        color_window.grab_set()

        # 居中显示
        color_window.update_idletasks()
        x = (color_window.winfo_screenwidth() // 2) - (500 // 2)
        y = (color_window.winfo_screenheight() // 2) - (600 // 2)
        color_window.geometry(f"500x600+{x}+{y}")

        # 当前颜色值
        current_rgb = self.yellow_text_rgb or {"r": 174, "g": 159, "b": 112}
        temp_r = tk.IntVar(value=current_rgb["r"])
        temp_g = tk.IntVar(value=current_rgb["g"])
        temp_b = tk.IntVar(value=current_rgb["b"])

        # 创建主框架
        main_frame = ttk.Frame(color_window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = tk.Label(main_frame, text="黄字颜色选择器",
                              font=(self.app.default_font, 16, "bold"))
        title_label.pack(pady=(0, 20))

        # 从config_manager获取预设颜色列表
        preset_colors = [(name, rgb["r"], rgb["g"], rgb["b"])
                        for name, rgb in self.yellow_text_presets.items()]

        # 创建预设颜色按钮（2行4列布局）
        for i, (name, r, g, b) in enumerate(preset_colors):
            row = i // 4
            col = i % 4
            color_hex = f"#{r:02x}{g:02x}{b:02x}"

            btn = tk.Button(main_frame, text=name, bg=color_hex, fg="white" if sum([r, g, b]) < 400 else "black",
                           width=10, height=2,
                           command=lambda r=r, g=g, b=b: self._set_preset_color_in_picker(r, g, b, temp_r, temp_g, temp_b, hex_var, color_preview))
            btn.grid(row=row, column=col, padx=2, pady=2)

        # 定义颜色更新函数
        def update_color_from_hex(*args):
            try:
                hex_value = hex_var.get().strip()
                if hex_value.startswith('#'):
                    hex_value = hex_value[1:]
                if len(hex_value) == 6:
                    r = int(hex_value[0:2], 16)
                    g = int(hex_value[2:4], 16)
                    b = int(hex_value[4:6], 16)
                    temp_r.set(r)
                    temp_g.set(g)
                    temp_b.set(b)
            except (ValueError, IndexError):
                pass

        def update_color_from_rgb(*args):
            try:
                r = max(0, min(255, temp_r.get()))
                g = max(0, min(255, temp_g.get()))
                b = max(0, min(255, temp_b.get()))
                hex_color = f"#{r:02x}{g:02x}{b:02x}"
                hex_var.set(hex_color.upper())
                color_preview.config(bg=hex_color)
            except:
                pass

        # RGB滑块
        rgb_frame = ttk.LabelFrame(main_frame, text="RGB调节", padding="10")
        rgb_frame.pack(fill=tk.X, pady=10)

        # R滑块
        tk.Label(rgb_frame, text="红色 (R):").grid(row=0, column=0, sticky=tk.W)
        r_scale = tk.Scale(rgb_frame, from_=0, to=255, orient=tk.HORIZONTAL, variable=temp_r, command=update_color_from_rgb)
        r_scale.grid(row=0, column=1, sticky=tk.EW, padx=5)
        r_entry = tk.Entry(rgb_frame, textvariable=temp_r, width=5)
        r_entry.grid(row=0, column=2)
        r_entry.bind('<KeyRelease>', update_color_from_rgb)

        # G滑块
        tk.Label(rgb_frame, text="绿色 (G):").grid(row=1, column=0, sticky=tk.W)
        g_scale = tk.Scale(rgb_frame, from_=0, to=255, orient=tk.HORIZONTAL, variable=temp_g, command=update_color_from_rgb)
        g_scale.grid(row=1, column=1, sticky=tk.EW, padx=5)
        g_entry = tk.Entry(rgb_frame, textvariable=temp_g, width=5)
        g_entry.grid(row=1, column=2)
        g_entry.bind('<KeyRelease>', update_color_from_rgb)

        # B滑块
        tk.Label(rgb_frame, text="蓝色 (B):").grid(row=2, column=0, sticky=tk.W)
        b_scale = tk.Scale(rgb_frame, from_=0, to=255, orient=tk.HORIZONTAL, variable=temp_b, command=update_color_from_rgb)
        b_scale.grid(row=2, column=1, sticky=tk.EW, padx=5)
        b_entry = tk.Entry(rgb_frame, textvariable=temp_b, width=5)
        b_entry.grid(row=2, column=2)
        b_entry.bind('<KeyRelease>', update_color_from_rgb)

        rgb_frame.columnconfigure(1, weight=1)

        # 十六进制输入
        hex_frame = ttk.LabelFrame(main_frame, text="十六进制颜色", padding="10")
        hex_frame.pack(fill=tk.X, pady=10)

        hex_var = tk.StringVar(value=f"#{current_rgb['r']:02x}{current_rgb['g']:02x}{current_rgb['b']:02x}".upper())
        hex_var.trace('w', update_color_from_hex)

        tk.Label(hex_frame, text="颜色代码:").pack(side=tk.LEFT)
        hex_entry = tk.Entry(hex_frame, textvariable=hex_var, width=10)
        hex_entry.pack(side=tk.LEFT, padx=5)

        # 颜色预览
        color_preview = tk.Label(hex_frame, text="预览", width=10, height=2,
                                bg=f"#{current_rgb['r']:02x}{current_rgb['g']:02x}{current_rgb['b']:02x}")
        color_preview.pack(side=tk.LEFT, padx=10)

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=20)

        # 应用按钮
        apply_btn = tk.Button(button_frame, text="应用颜色",
                             command=lambda: self.apply_color_from_picker(temp_r.get(), temp_g.get(), temp_b.get(), color_window),
                             bg="#4CAF50", fg="white", width=12, height=2)
        apply_btn.pack(side=tk.LEFT, padx=5)

        # 实时预览按钮
        preview_btn = tk.Button(button_frame, text="实时预览",
                               command=lambda: self.apply_color_from_picker(temp_r.get(), temp_g.get(), temp_b.get()),
                               bg="#2196F3", fg="white", width=12, height=2)
        preview_btn.pack(side=tk.LEFT, padx=5)

        # 保存为预设按钮
        def save_as_preset():
            name = tk.simpledialog.askstring("保存预设", "请输入预设名称:")
            if name and name.strip():
                name = name.strip()
                if name in self.yellow_text_presets:
                    if not messagebox.askyesno("确认", f"预设 '{name}' 已存在，是否覆盖？"):
                        return

                self.save_custom_yellow_color(name, temp_r.get(), temp_g.get(), temp_b.get())
                messagebox.showinfo("成功", f"预设 '{name}' 已保存")

                # 更新菜单
                if hasattr(self.app, 'ui_components'):
                    self.app.ui_components.update_yellow_color_menu()

        import tkinter.simpledialog
        save_preset_btn = tk.Button(button_frame, text="保存为预设", command=save_as_preset,
                                   bg="#FF9800", fg="white", width=12, height=2)
        save_preset_btn.pack(side=tk.LEFT, padx=5)

    def _set_preset_color_in_picker(self, r, g, b, temp_r, temp_g, temp_b, hex_var, color_preview):
        """在颜色选择器中设置预设颜色"""
        temp_r.set(r)
        temp_g.set(g)
        temp_b.set(b)
        hex_color = f"#{r:02x}{g:02x}{b:02x}"
        hex_var.set(hex_color.upper())
        color_preview.config(bg=hex_color)

    def load_play_count_setting(self):
        """从数据库加载播放次数设置"""
        try:
            result = self.app.safe_db_execute(
                "SELECT value FROM settings WHERE key = ?", ("play_count",), fetch=True
            )

            if result and len(result) > 0:
                # result是列表，每个元素是元组，取第一个元组的第一个元素
                value = result[0][0]

                if value == "-1":
                    self.app.target_play_count = -1
                else:
                    try:
                        count = int(value)
                        if count > 0:
                            self.app.target_play_count = count
                    except ValueError:
                        pass  # 使用默认值

                print(f"加载播放次数设置: {self.app.target_play_count}")
            else:
                print("使用默认播放次数设置: 5次")

            # 同步循环模式设置
            loop_enabled = self.app.target_play_count == -1
            if hasattr(self.app, 'loop_enabled'):
                self.app.loop_enabled = loop_enabled
            # print(f"循环模式: {'启用' if loop_enabled else '禁用'}")

        except Exception as e:
            print(f"加载播放次数设置失败: {e}")

    def save_play_count_setting(self):
        """保存播放次数设置到数据库"""
        try:
            self.app.safe_db_execute(
                """
                INSERT OR REPLACE INTO settings (key, value)
                VALUES (?, ?)
            """,
                ("play_count", str(self.app.target_play_count)),
            )
            print(f"播放次数设置已保存: {self.app.target_play_count}")
        except Exception as e:
            print(f"保存播放次数设置失败: {e}")

    def load_scroll_speed_settings(self):
        """从数据库加载滚动速度设置"""
        try:
            import sqlite3
            with sqlite3.connect(self.app.db_path) as conn:
                cursor = conn.execute(
                    "SELECT value FROM ui_settings WHERE key = 'scroll_speed'"
                )
                result = cursor.fetchone()
                if result:
                    try:
                        self.app.scroll_duration = float(result[0])
                        print(f"加载滚动速度设置: {self.app.scroll_duration}秒")
                    except (ValueError, TypeError):
                        print("滚动速度设置格式错误，使用默认值")
                        self.app.scroll_duration = 0.0
                else:
                    print("未找到滚动速度设置，使用默认值")
                    self.app.scroll_duration = 0.0
        except Exception as e:
            print(f"加载滚动速度设置失败: {e}")
            self.app.scroll_duration = 0.0

    def save_scroll_speed_settings(self):
        """保存滚动速度设置到数据库"""
        try:
            import sqlite3
            with sqlite3.connect(self.app.db_path) as conn:
                conn.execute(
                    """
                    INSERT OR REPLACE INTO ui_settings (key, value)
                    VALUES (?, ?)
                """,
                    ("scroll_speed", str(self.app.scroll_duration)),
                )
                conn.commit()
                print(f"已保存滚动速度设置: {self.app.scroll_duration}秒")
        except Exception as e:
            print(f"保存滚动速度设置失败: {e}")

    def load_display_mode_settings(self):
        """从数据库加载显示模式设置"""
        try:
            import sqlite3
            with sqlite3.connect(self.app.db_path) as conn:
                cursor = conn.execute(
                    "SELECT value FROM ui_settings WHERE key = 'display_mode'"
                )
                result = cursor.fetchone()
                if result:
                    self.app.original_display_mode = result[0]
                    print(f"加载显示模式设置: {self.app.original_display_mode}")
                else:
                    print("未找到显示模式设置，使用默认值")
                    self.app.original_display_mode = "stretch"
        except Exception as e:
            print(f"加载显示模式设置失败: {e}")
            self.app.original_display_mode = "stretch"

    def save_display_mode_settings(self):
        """保存显示模式设置到数据库"""
        try:
            import sqlite3
            with sqlite3.connect(self.app.db_path) as conn:
                conn.execute(
                    """
                    INSERT OR REPLACE INTO ui_settings (key, value)
                    VALUES (?, ?)
                """,
                    ("display_mode", self.app.original_display_mode),
                )
                conn.commit()
                print(f"已保存显示模式设置: {self.app.original_display_mode}")
        except Exception as e:
            print(f"保存显示模式设置失败: {e}")

    def set_scroll_easing(self, easing_key):
        """设置全局滚动函数类型"""
        self.app.current_easing = easing_key
        if hasattr(self.app, 'scroll_easing_var'):
            self.app.scroll_easing_var.set(easing_key)

        # 保存到config.json
        import json
        try:
            config = {}
            if self.config_file.exists():
                with open(self.config_file, "r", encoding="utf-8") as f:
                    config = json.load(f)

            config["scroll_easing"] = easing_key

            with open(self.config_file, "w", encoding="utf-8") as f:
                json.dump(config, f, indent=2, ensure_ascii=False)

            print(f"滚动缓动设置已保存: {easing_key}")
        except Exception as e:
            print(f"保存滚动缓动设置失败: {e}")

    def batch_load_projects(self, saved_items):
        """批量加载项目

        Args:
            saved_items: 保存的项目列表
        """
        # 预先清空项目树
        for item in self.app.project_tree.get_children():
            self.app.project_tree.delete(item)

        self.app.image_items.clear()

        # 批量插入项目
        for item in saved_items:
            try:
                abs_path = Path(item["path"]).resolve()
                if abs_path.exists():
                    # 添加到项目表
                    project_item = {
                        "id": item["id"],
                        "name": item["name"],
                        "path": abs_path,
                    }
                    self.app.image_items.append(project_item)

                    item_text = f"{project_item['id']}. {project_item['name']}"
                    self.app.project_tree.insert(
                        "", "end", text=item_text, iid=str(project_item["id"])
                    )
            except Exception as e:
                print(f"加载项目失败: {item.get('name', '未知')}, 错误: {e}")

        # 批量更新强制刷新界面
        self.app.project_tree.update_idletasks()
