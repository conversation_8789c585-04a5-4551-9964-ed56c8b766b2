#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
投影屏幕GPU直接渲染器 - gpu_projection_renderer.py
为投影屏幕提供独立的GPU硬件加速渲染能力

特性：
- 独立GPU上下文和渲染管道
- 与主屏幕并行GPU渲染
- 支持全屏无边框GPU窗口
- 实时纹理共享和同步
- 高性能双屏GPU渲染
"""

import moderngl
import pygame
import numpy as np
import time
import threading
from PIL import Image
from pathlib import Path
import tkinter as tk

try:
    from OpenGL import GL as gl
    OPENGL_AVAILABLE = True
except ImportError:
    OPENGL_AVAILABLE = False

class GPUProjectionRenderer:
    """投影屏幕GPU直接渲染器"""
    
    def __init__(self, screen_info, main_gpu_bridge=None):
        """初始化GPU投影渲染器
        
        Args:
            screen_info: 投影屏幕信息 {x, y, width, height}
            main_gpu_bridge: 主屏幕GPU桥接器（用于纹理共享）
        """
        self.screen_info = screen_info
        self.main_gpu_bridge = main_gpu_bridge
        
        # 渲染状态
        self.is_active = False
        self.current_texture = None
        self.current_image_path = None
        
        # 滚动状态
        self.current_scroll_pos = 0.0
        self.target_scroll_pos = 0.0
        self.is_scrolling = False
        
        # GPU上下文和渲染对象
        self.ctx = None
        self.program = None
        self.vao = None
        self.vbo = None
        self.surface = None
        self.clock = None
        
        # 纹理缓存
        self.texture_cache = {}
        self.max_cache_size = 20
        
        # 性能监控
        self.frame_count = 0
        self.fps_start_time = time.time()
        self.current_fps = 0
        
        pass
        
    def initialize_gpu_context(self):
        """初始化独立的GPU上下文"""
        try:
            # 初始化pygame显示系统
            pygame.init()
            
            # 创建全屏GPU窗口
            screen_flags = pygame.OPENGL | pygame.DOUBLEBUF | pygame.NOFRAME
            
            # 设置窗口位置
            import os
            os.environ['SDL_VIDEO_WINDOW_POS'] = f"{self.screen_info['x']},{self.screen_info['y']}"
            
            self.surface = pygame.display.set_mode(
                (self.screen_info['width'], self.screen_info['height']),
                screen_flags
            )
            
            pygame.display.set_caption("GPU投影渲染")
            
            # 创建ModernGL上下文
            self.ctx = moderngl.create_context()
            
            pass
            
            # 设置视口
            self.ctx.viewport = (0, 0, self.screen_info['width'], self.screen_info['height'])
            
            # 设置VSYNC
            pygame.display.gl_set_attribute(pygame.GL_SWAP_CONTROL, 1)
            
            # 初始化着色器和缓冲区
            self._setup_shaders()
            self._setup_buffers()
            
            # 创建时钟对象
            self.clock = pygame.time.Clock()
            
            return True
            
        except Exception as e:
            print(f"❌ GPU投影上下文初始化失败: {e}")
            return False
    
    def _setup_shaders(self):
        """设置GPU着色器程序"""
        
        # 投影专用顶点着色器
        vertex_shader = """
        #version 330 core
        
        in vec2 in_position;
        in vec2 in_texcoord;
        
        uniform mat4 projection;
        uniform float scroll_offset;
        uniform vec2 scale_factor;
        uniform vec2 translation;
        
        out vec2 texcoord;
        
        void main() {
            vec2 pos = in_position;
            
            // 应用缩放
            pos *= scale_factor;
            
            // 应用滚动偏移
            pos.y += scroll_offset;
            
            // 应用平移
            pos += translation;
            
            gl_Position = projection * vec4(pos, 0.0, 1.0);
            texcoord = in_texcoord;
        }
        """
        
        # 投影专用片段着色器
        fragment_shader = """
        #version 330 core
        
        in vec2 texcoord;
        out vec4 fragColor;
        
        uniform sampler2D image_texture;
        uniform bool invert_colors;
        uniform bool yellow_text_mode;
        uniform vec3 yellow_color;
        uniform float alpha;
        uniform float brightness;
        uniform float contrast;
        
        void main() {
            vec4 color = texture(image_texture, texcoord);
            
            // 亮度和对比度调整
            color.rgb = ((color.rgb - 0.5) * contrast + 0.5) * brightness;
            
            if (yellow_text_mode) {
                // 高质量黄字效果
                float luminance = dot(color.rgb, vec3(0.299, 0.587, 0.114));
                if (luminance > 0.5) {
                    color.rgb = yellow_color;
                } else {
                    color.rgb = vec3(0.0);
                }
            } else if (invert_colors) {
                color.rgb = vec3(1.0) - color.rgb;
            }
            
            color.a *= alpha;
            fragColor = color;
        }
        """
        
        # 编译着色器程序
        self.program = self.ctx.program(
            vertex_shader=vertex_shader,
            fragment_shader=fragment_shader
        )
        
        print("✅ GPU投影着色器编译成功")
    
    def _setup_buffers(self):
        """设置GPU缓冲区"""
        
        # 创建正交投影矩阵（适配投影屏幕）
        left = -1.0
        right = 1.0
        bottom = -1.0
        top = 1.0
        near = -1.0
        far = 1.0
        
        projection_matrix = np.array([
            [2.0/(right-left), 0, 0, -(right+left)/(right-left)],
            [0, 2.0/(top-bottom), 0, -(top+bottom)/(top-bottom)],
            [0, 0, -2.0/(far-near), -(far+near)/(far-near)],
            [0, 0, 0, 1]
        ], dtype=np.float32)
        
        self.projection_matrix = projection_matrix.flatten()
        
        # 创建全屏四边形顶点缓冲区
        vertices = np.array([
            # 位置        纹理坐标
            -1.0, -1.0,   0.0, 1.0,
             1.0, -1.0,   1.0, 1.0,
             1.0,  1.0,   1.0, 0.0,
            -1.0,  1.0,   0.0, 0.0,
        ], dtype=np.float32)
        
        indices = np.array([0, 1, 2, 2, 3, 0], dtype=np.uint32)
        
        # 创建缓冲区对象
        self.vbo = self.ctx.buffer(vertices.tobytes())
        ibo = self.ctx.buffer(indices.tobytes())
        
        # 创建顶点数组对象
        self.vao = self.ctx.vertex_array(
            self.program,
            [(self.vbo, '2f 2f', 'in_position', 'in_texcoord')],
            ibo
        )
        
        print("✅ GPU投影缓冲区创建成功")
    
    def load_texture_from_main_bridge(self, image_path):
        """从主GPU桥接器加载纹理（纹理共享）"""
        # 跨GPU上下文纹理共享是复杂的操作，暂时禁用
        # 直接返回None，让系统使用直接加载方式
        return None
    
    def _create_texture_copy(self, source_texture):
        """创建纹理副本到投影GPU上下文"""
        try:
            if not self.ctx or not source_texture:
                return None
            
            # 获取源纹理的数据
            # 注意：这里需要在不同GPU上下文间共享纹理，这是一个复杂的操作
            # 简化版本：直接返回None，让系统使用直接加载方式
            print("⚠️  纹理共享暂未实现，使用直接加载方式")
            return None
            
        except Exception as e:
            print(f"⚠️  创建纹理副本失败: {e}")
            return None
    
    def load_texture_direct(self, image_path):
        """直接加载纹理到投影GPU上下文"""
        image_path = str(image_path)
        
        # 检查缓存
        if image_path in self.texture_cache:
            return self.texture_cache[image_path]
        
        try:
            # 使用PIL加载图片
            with Image.open(image_path) as img:
                img = img.convert('RGBA')
                
                # 检查尺寸限制
                max_size = self.ctx.info['GL_MAX_TEXTURE_SIZE']
                if img.width > max_size or img.height > max_size:
                    ratio = min(max_size / img.width, max_size / img.height)
                    new_size = (int(img.width * ratio), int(img.height * ratio))
                    img = img.resize(new_size, Image.Resampling.LANCZOS)
                
                # 创建GPU纹理
                texture = self.ctx.texture(img.size, 4, img.tobytes())
                texture.filter = (moderngl.LINEAR, moderngl.LINEAR)
                
                try:
                    clamp_mode = moderngl.CLAMP_TO_EDGE
                except AttributeError:
                    clamp_mode = 0x812F
                texture.wrap_x = clamp_mode
                texture.wrap_y = clamp_mode
                
                # 管理缓存大小
                if len(self.texture_cache) >= self.max_cache_size:
                    oldest_key = next(iter(self.texture_cache))
                    old_texture = self.texture_cache.pop(oldest_key)
                    old_texture.release()
                
                self.texture_cache[image_path] = texture
                
                print(f"✅ 投影GPU纹理加载成功: {Path(image_path).name} ({img.width}x{img.height})")
                return texture
                
        except Exception as e:
            print(f"❌ 投影GPU纹理加载失败: {image_path}, 错误: {e}")
            return None
    
    def set_image(self, image_path):
        """设置要显示的图片"""
        try:
            # 确保GPU上下文已初始化
            if not self.ctx:
                if not self.initialize_gpu_context():
                    print("❌ GPU上下文初始化失败，无法设置图片")
                    return False
            
            # 优先尝试从主GPU桥接器共享纹理
            texture = self.load_texture_from_main_bridge(image_path)
            
            # 如果共享失败，直接加载
            if not texture:
                texture = self.load_texture_direct(image_path)
            
            if texture:
                self.current_texture = texture
                self.current_image_path = str(image_path)
                return True
            else:
                print("❌ GPU投影图片设置失败")
                return False
                
        except Exception as e:
            print(f"❌ 设置投影图片失败: {e}")
            return False
    
    def set_scroll_position(self, position):
        """设置滚动位置（0.0-1.0）"""
        self.current_scroll_pos = max(0.0, min(1.0, position))
    
    def smooth_scroll_to(self, target_position, duration=1.0):
        """平滑滚动到目标位置"""
        self.target_scroll_pos = max(0.0, min(1.0, target_position))
        # 这里可以实现GPU级别的平滑滚动动画
        # 目前简化为直接设置位置
        self.current_scroll_pos = self.target_scroll_pos
    
    def render_frame(self, invert_colors=False, yellow_text_mode=False, 
                    yellow_color=(1.0, 1.0, 0.0), alpha=1.0, 
                    brightness=1.0, contrast=1.0):
        """渲染一帧"""
        if not self.current_texture or not self.ctx:
            return False
        
        try:
            # 清空画布
            self.ctx.clear(0.0, 0.0, 0.0, 1.0)
            
            # 绑定纹理
            self.current_texture.use(0)
            
            # 计算缩放和平移
            screen_aspect = self.screen_info['width'] / self.screen_info['height']
            texture_aspect = self.current_texture.width / self.current_texture.height
            
            if texture_aspect > screen_aspect:
                # 图片更宽，按宽度缩放
                scale_x = 1.0
                scale_y = screen_aspect / texture_aspect
            else:
                # 图片更高，按高度缩放
                scale_x = texture_aspect / screen_aspect
                scale_y = 1.0
            
            # 设置着色器参数
            self.program['projection'].write(self.projection_matrix.tobytes())
            self.program['scroll_offset'].value = self.current_scroll_pos * 2.0 - 1.0
            self.program['scale_factor'].value = (scale_x, scale_y)
            self.program['translation'].value = (0.0, 0.0)
            self.program['image_texture'].value = 0
            self.program['invert_colors'].value = invert_colors
            self.program['yellow_text_mode'].value = yellow_text_mode
            self.program['yellow_color'].value = yellow_color
            self.program['alpha'].value = alpha
            self.program['brightness'].value = brightness
            self.program['contrast'].value = contrast
            
            # 渲染
            self.vao.render()
            
            # 交换缓冲区
            pygame.display.flip()
            
            # 更新FPS统计
            self._update_fps_counter()
            
            return True
            
        except Exception as e:
            print(f"❌ 投影渲染失败: {e}")
            return False
    
    def _update_fps_counter(self):
        """更新FPS计数器"""
        self.frame_count += 1
        current_time = time.time()
        
        if current_time - self.fps_start_time >= 1.0:
            self.current_fps = self.frame_count / (current_time - self.fps_start_time)
            self.frame_count = 0
            self.fps_start_time = current_time
    
    def start_rendering(self):
        """启动渲染循环"""
        # 确保GPU上下文已初始化
        if not self.ctx:
            if not self.initialize_gpu_context():
                return False
        
        self.is_active = True
        
        def render_loop():
            while self.is_active:
                # 处理pygame事件
                for event in pygame.event.get():
                    if event.type == pygame.QUIT:
                        self.is_active = False
                        break
                    elif event.type == pygame.KEYDOWN:
                        if event.key == pygame.K_ESCAPE:
                            self.is_active = False
                            break
                
                # 渲染当前帧
                if self.current_texture:
                    self.render_frame()
                
                # 控制帧率
                self.clock.tick(120)  # 120fps
        
        # 在单独线程中运行渲染循环
        self.render_thread = threading.Thread(target=render_loop, daemon=True)
        self.render_thread.start()
        
        print("🚀 GPU投影渲染循环已启动")
        return True
    
    def stop_rendering(self):
        """停止渲染循环"""
        self.is_active = False
        
        if hasattr(self, 'render_thread'):
            self.render_thread.join(timeout=1.0)
        
        self.cleanup()
        print("✅ GPU投影渲染已停止")
    
    def get_performance_info(self):
        """获取性能信息"""
        return {
            'fps': self.current_fps,
            'gpu_renderer': self.ctx.info['GL_RENDERER'] if self.ctx else 'Unknown',
            'opengl_version': self.ctx.info['GL_VERSION'] if self.ctx else 'Unknown',
            'texture_cache_size': len(self.texture_cache),
            'screen_resolution': f"{self.screen_info['width']}x{self.screen_info['height']}",
            'is_active': self.is_active
        }
    
    def cleanup(self):
        """清理GPU资源"""
        try:
            # 清理纹理缓存
            for texture in self.texture_cache.values():
                texture.release()
            self.texture_cache.clear()
            
            # 清理GPU对象
            if self.vao:
                self.vao.release()
            if self.vbo:
                self.vbo.release()
            if self.program:
                self.program.release()
            
            # 清理pygame
            pygame.quit()
            
            print("✅ GPU投影资源已清理")
            
        except Exception as e:
            print(f"⚠️  GPU投影资源清理时出错: {e}")


# 工厂函数
def create_gpu_projection_renderer(screen_info, main_gpu_bridge=None):
    """创建GPU投影渲染器
    
    Args:
        screen_info: 屏幕信息字典 {x, y, width, height}
        main_gpu_bridge: 主GPU桥接器（可选）
    
    Returns:
        GPUProjectionRenderer实例或None
    """
    try:
        if not OPENGL_AVAILABLE:
            print("❌ OpenGL不可用，无法创建GPU投影渲染器")
            return None
        
        renderer = GPUProjectionRenderer(screen_info, main_gpu_bridge)
        return renderer
        
    except Exception as e:
        print(f"❌ 创建GPU投影渲染器失败: {e}")
        return None


# 测试代码
def test_gpu_projection_renderer():
    """测试GPU投影渲染器"""
    print("🧪 GPU投影渲染器测试开始...")
    
    # 模拟屏幕信息
    screen_info = {
        'x': 1920,
        'y': 0,
        'width': 1920,
        'height': 1080
    }
    
    renderer = create_gpu_projection_renderer(screen_info)
    
    if renderer:
        print("✅ GPU投影渲染器创建成功")
        
        # 显示性能信息
        info = renderer.get_performance_info()
        print(f"GPU信息: {info['gpu_renderer']}")
        print(f"OpenGL版本: {info['opengl_version']}")
        print(f"屏幕分辨率: {info['screen_resolution']}")
        
        # 清理
        renderer.cleanup()
        print("✅ GPU投影渲染器测试通过!")
    else:
        print("❌ GPU投影渲染器创建失败")


if __name__ == "__main__":
    test_gpu_projection_renderer()
