import json
import re
import sqlite3
import sys
import time
import tkinter as tk
from concurrent.futures import ThreadPoolExecutor
from pathlib import Path
from tkinter import filedialog, messagebox, ttk
import numpy as np
import pypinyin

# from tkinterdnd2 import DND_FILES, TkinterDnD  # 移除拖放功能，简化打包
from cachetools import LRUCache
from database_manager import DatabaseManager
from keytime import AutoPlayer, KeyTimeRecorder
from PIL import Image, ImageTk
from yuantu import YuanTuManager
from projection_manager import ProjectionManager
from ui_components import UIComponents
from smooth_scroll_engine import VsyncOptimizedScrollEngine
from pynput import keyboard as pynput_keyboard
from config_manager import ConfigManager
from keyframe_manager import KeyframeManager
from project_manager import ProjectManager

# 全局变量用于存储应用实例
app_instance = None


class ImageProjector:
    def __init__(self, root):
        """初始化应用"""

        self.root = root
        self.root.title("Canvas Cast V2.2")
        
        # 先隐藏窗口，避免显示小窗口的闪烁
        self.root.withdraw()
        
        print(f"ImageProjector初始化开始")

        # 设置窗口图标
        try:
            # 兼容PyInstaller和Nuitka打包后的路径
            if getattr(sys, 'frozen', False):
                # 如果是打包的程序
                current_dir = Path(sys.executable).parent
            else:
                # 如果是直接运行的Python脚本
                current_dir = Path.cwd()
            
            icon_path = current_dir / "baodian.ico"
            if icon_path.exists():
                self.root.iconbitmap(str(icon_path))
                print(f"已设置窗口图标: {icon_path}")
            else:
                print(f"图标文件不存在: {icon_path}")
        except Exception as e:
            print(f"设置窗口图标失败: {e}")

        # 设置默认字体
        self.default_font = "Microsoft YaHei UI"

        # 修改：兼容PyInstaller打包后的路径
        if getattr(sys, 'frozen', False):
            # 如果是PyInstaller打包的程序
            current_dir = Path(sys.executable).parent
        else:
            # 如果是直接运行的Python脚本
            current_dir = Path.cwd()
            
        self.db_path = current_dir / "pyimages.db"
        print(f"数据库路径: {self.db_path}")

        # 确保数据库目录存在
        self.db_path.parent.mkdir(parents=True, exist_ok=True)

        # 配置文件路径将由config_manager管理

        # 初始化数据库管理器
        self.db_manager = DatabaseManager(str(self.db_path), enable_optimizations=True)

        # 初始化数据库结构管理器
        from schema_db import DatabaseSchema
        self.db_schema = DatabaseSchema(str(self.db_path))

        # 初始化项目数据库操作器
        from project_db import ProjectDatabaseOperations
        self.project_db = ProjectDatabaseOperations(str(self.db_path))

        # 初始化数据库 - 只需要一次
        self.db_schema.init_database()

        # 初始化时间录制和自动播放模块
        self.init_keytime_modules()

        # 初始化项目管理器（在关键帧状态之前）
        self.project_manager = ProjectManager(self)

        # 初始化播放控制器
        from playback_controller import PlaybackController
        self.playback_controller = PlaybackController(self)

        # 设置播放控制器方法代理
        self.set_play_count = self.playback_controller.set_play_count
        self._update_play_count_button = self.playback_controller._update_play_count_button
        self.clear_timing_data = self.playback_controller.clear_timing_data
        self._clear_normal_timing_data = self.playback_controller._clear_normal_timing_data
        self._clear_original_mode_timing_data = self.playback_controller._clear_original_mode_timing_data

        # 初始化上下文菜单管理器
        from context_menu_manager import ContextMenuManager
        self.context_menu_manager = ContextMenuManager(self)

        # 初始化事件处理器
        from event_handler import EventHandler
        self.event_handler = EventHandler(self)

        # 现在可以设置播放结束回调函数
        if hasattr(self, 'auto_player') and self.auto_player:
            self.auto_player.on_play_finished = self.playback_controller.on_auto_play_finished

        # 初始化关键帧状态
        self._init_keyframe_state()

        # 初始化UI组件管理器
        self.ui_components = UIComponents(self)

        # 添加缓存大小限制
        self.max_cache_size = 100  # 设置最大缓存数量

        self.thread_pool = ThreadPoolExecutor(max_workers=4)

        # 初始化配置管理器
        self.config_manager = ConfigManager(self)

        # 添加配置管理器方法代理
        self.set_yellow_text_color = self.config_manager.set_yellow_text_color
        self.apply_color_from_picker = self.config_manager.apply_color_from_picker
        self.save_custom_yellow_color = self.config_manager.save_custom_yellow_color
        self.load_play_count_setting = self.config_manager.load_play_count_setting
        self.save_play_count_setting = self.config_manager.save_play_count_setting
        self.load_scroll_speed_settings = self.config_manager.load_scroll_speed_settings
        self.save_scroll_speed_settings = self.config_manager.save_scroll_speed_settings
        self.load_display_mode_settings = self.config_manager.load_display_mode_settings
        self.save_display_mode_settings = self.config_manager.save_display_mode_settings
        self.set_scroll_easing = self.config_manager.set_scroll_easing

        # 添加播放控制器方法代理
        self.toggle_timing_recording = self.playback_controller.toggle_timing_recording
        self.toggle_auto_play = self.playback_controller.toggle_auto_play
        self.set_play_count = self.playback_controller.set_play_count
        self.clear_timing_data = self.playback_controller.clear_timing_data
        self.toggle_countdown_pause = self.playback_controller.toggle_countdown_pause

        # 添加上下文菜单管理器方法代理
        self.show_context_menu = self.context_menu_manager.show_context_menu
        self.delete_selected_item = self.context_menu_manager.delete_selected_item
        self.mark_as_original = self.context_menu_manager.mark_as_original
        self.unmark_original = self.context_menu_manager.unmark_original
        self.update_project_tree_marks = self.context_menu_manager.update_project_tree_marks

        # 添加事件处理器方法代理
        self.on_mousewheel = self.event_handler.on_mousewheel
        self.handle_arrow_key = self.event_handler.handle_arrow_key
        self.handle_pageup_key = self.event_handler.handle_pageup_key
        self.handle_pagedown_key = self.event_handler.handle_pagedown_key
        self.handle_escape_key = self.event_handler.handle_escape_key
        self.on_tree_press = self.event_handler.on_tree_press
        self.on_tree_motion = self.event_handler.on_tree_motion
        self.on_tree_release = self.event_handler.on_tree_release

        # 现在可以安全地加载播放次数设置
        self.load_play_count_setting()

        # UI更新调度器
        self._ui_update_scheduled = False

        # 为了向后兼容，添加属性代理
        @property
        def yellow_text_presets(self):
            return self.config_manager.yellow_text_presets

        @property
        def current_yellow_color_name(self):
            return self.config_manager.current_yellow_color_name

        @current_yellow_color_name.setter
        def current_yellow_color_name(self, value):
            self.config_manager.current_yellow_color_name = value

        @property
        def yellow_text_rgb(self):
            return self.config_manager.yellow_text_rgb

        @yellow_text_rgb.setter
        def yellow_text_rgb(self, value):
            self.config_manager.yellow_text_rgb = value

        # 绑定属性到实例
        self.__class__.yellow_text_presets = yellow_text_presets
        self.__class__.current_yellow_color_name = current_yellow_color_name
        self.__class__.yellow_text_rgb = yellow_text_rgb

        self.image_items = []  # 初始化项目列表
        self.last_scroll_time = 0
        self.scroll_update_delay = 16  # 固定延迟
        self.is_scrolling = False
        self.scroll_timer = None

        # 添加原图模式标志
        self.original_mode = False

        # 添加原图模式显示类型：'fit'(适中) 或 'stretch'(拉伸)
        self.original_display_mode = 'stretch'

        # 创建所有UI组件（包括布局、canvas、滚动条等）
        self.ui_components.create_all_components(create_layout=True)

        # 注意：load_projects移到projection_manager初始化后调用

        self.config_manager.load_config()

        # 如果配置文件不存在，自动生成默认配置文件
        if not self.config_file.exists():
            self.config_manager.save_config()

        # 确保在加载配置后更新黄字颜色菜单（包含自定义颜色）
        self.root.after(300, lambda: self.ui_components.update_yellow_color_menu())

        # 添加关键帧相关的状态变量
        self.current_image_id = None
        self.current_keyframe_index = -1
        self.keyframe_markers = []

        # 窗口事件绑定和UI组件创建已迁移到ui_components.py
        # 关键帧指示器和预览线已在ui_components中创建
        self.current_scroll_position = 0

        # 加载滚动速度设置
        self.load_scroll_speed_settings()

        # 加载显示模式设置
        self.load_display_mode_settings()

        # 启动时自动同步文件夹内容（使用优化版本）
        self.root.after(1000, self.quick_sync_check)

        # 设置全局应用实例
        global app_instance
        app_instance = self
        
        # 在所有UI组件创建完成后，延迟设置窗口最大化
        self.root.after(100, self._delayed_maximize)
    
    def _delayed_maximize(self):
        """延迟最大化窗口，确保在UI组件完全加载后执行"""
        try:
            # 先尝试正常最大化（窗口仍然隐藏）
            self.root.state("zoomed")
            self.root.update_idletasks()
            
            # 显示窗口（此时已经是最大化状态）
            self.root.deiconify()
            
            # 简单验证最大化是否成功
            self.root.after(50, self._verify_maximize)
            
        except Exception as e:
            print(f"窗口最大化失败: {e}")
            # 如果出错，至少要显示窗口
            self.root.deiconify()
    
    def _verify_maximize(self):
        """验证最大化是否成功，如果不成功则强制设置"""
        try:
            current_width = self.root.winfo_width()
            current_height = self.root.winfo_height()
            tk_screen_width = self.root.winfo_screenwidth()
            tk_screen_height = self.root.winfo_screenheight()
            
            # 如果窗口高度明显小于屏幕高度，再次尝试最大化
            if current_height < tk_screen_height * 0.95:
                print("窗口未完全最大化，重新设置...")
                self.root.state("normal")  # 先恢复正常状态
                self.root.update_idletasks()
                self.root.state("zoomed")   # 再次最大化
                self.root.update_idletasks()
            
            # 只在启动时显示一次窗口状态确认
            print(f"窗口已启动 - 状态: {self.root.state()}, 尺寸: {current_width}x{current_height}")
                
        except Exception as e:
            print(f"验证窗口状态失败: {e}")
    
    @property
    def second_window(self):
        """投影窗口代理属性"""
        return self.projection_manager.second_window

    @second_window.setter
    def second_window(self, value):
        """投影窗口设置代理"""
        self.projection_manager.second_window = value

    @property
    def second_canvas(self):
        """投影画布代理属性"""
        return self.projection_manager.second_canvas

    @second_canvas.setter
    def second_canvas(self, value):
        """投影画布设置代理"""
        self.projection_manager.second_canvas = value

    @property
    def sync_enabled(self):
        """投影同步状态代理属性"""
        return self.projection_manager.sync_enabled

    @sync_enabled.setter
    def sync_enabled(self, value):
        """投影同步状态设置代理"""
        self.projection_manager.sync_enabled = value

    @property
    def global_hotkeys_enabled(self):
        """全局热键状态代理属性"""
        return self.projection_manager.global_hotkeys_enabled

    @global_hotkeys_enabled.setter
    def global_hotkeys_enabled(self, value):
        """全局热键状态设置代理"""
        self.projection_manager.global_hotkeys_enabled = value

    @property
    def screens(self):
        """屏幕列表代理属性"""
        return self.projection_manager.screens

    @screens.setter
    def screens(self, value):
        """屏幕列表设置代理"""
        self.projection_manager.screens = value

    # ==================== 投影管理器方法代理 ====================
    
    def toggle_projection(self):
        """切换投影显示状态"""
        return self.projection_manager.toggle_projection()

    def setup_global_hotkeys(self):
        """设置全局热键"""
        return self.projection_manager.setup_global_hotkeys()

    def cleanup_global_hotkeys(self):
        """清理全局热键"""
        return self.projection_manager.cleanup_global_hotkeys()

    def get_effective_screen_size(self, is_projection_screen=False):
        """计算有效显示区域"""
        return self.projection_manager.get_effective_screen_size(is_projection_screen)

    def get_monitor_info(self):
        """获取显示器信息"""
        return self.projection_manager.get_monitor_info()

    def create_monitor_menu(self, parent_frame):
        """创建显示器选择菜单"""
        return self.projection_manager.create_monitor_menu(parent_frame)

    def get_selected_monitor_index(self):
        """获取当前选择的显示器索引"""
        return self.projection_manager.get_selected_monitor_index()

    def show_on_second_screen(self):
        """显示到选定的屏幕"""
        return self.projection_manager.show_on_second_screen()

    def sync_projection_screen_absolute(self):
        """使用绝对像素位置同步投影屏幕"""
        return self.projection_manager.sync_projection_screen_absolute()

    def update_projection(self):
        """更新投影屏幕"""
        return self.projection_manager.update_projection()

    def update_second_screen(self):
        """更新第二屏幕显示"""
        return self.projection_manager.update_second_screen()

    def force_update_projection(self):
        """强制更新投影窗口"""
        return self.projection_manager.force_update_projection()

    # ==================== 数据库操作方法 ====================

    def safe_db_execute(self, query, params=None, fetch=False, commit=True):
        """统一的数据库操作方法，减少重复代码（向后兼容）"""
        return self.db_manager.safe_execute(query, params, fetch, commit)

    def execute_batch_operations(self, operations):
        """批量执行数据库操作的便利方法"""
        return self.db_manager.execute_batch(operations)

    def execute_many_operations(self, query, params_list):
        """执行多个相同查询的便利方法"""
        return self.db_manager.execute_many(query, params_list)

    def get_database_connection(self):
        """获取数据库连接的便利方法"""
        return self.db_manager.get_connection()

    def get_database_transaction(self):
        """获取数据库事务的便利方法"""
        return self.db_manager.transaction()

    def show_database_stats(self):
        """显示数据库性能统计信息"""
        try:
            stats = self.db_manager.get_database_stats()

            stats_text = f"""数据库性能统计信息：

数据库大小: {stats.get('database_size_mb', 'N/A')} MB
表数量: {stats.get('table_count', 'N/A')}
索引数量: {stats.get('index_count', 'N/A')}
日志模式: {stats.get('journal_mode', 'N/A')}

优化状态: {'已启用' if self.db_manager.enable_optimizations else '未启用'}
连接状态: {'已连接' if self.db_manager._conn else '未连接'}
"""

            messagebox.showinfo("数据库统计", stats_text)

        except Exception as e:
            messagebox.showerror("错误", f"获取数据库统计信息失败: {e}")

    def optimize_database_manually(self):
        """手动优化数据库"""
        try:
            self.db_manager.optimize_database()
            messagebox.showinfo("成功", "数据库优化完成")
        except Exception as e:
            messagebox.showerror("错误", f"数据库优化失败: {e}")

    # 数据库初始化已移动到schema_db.py模块

    def init_keytime_modules(self):
        """初始化时间录制和自动播放模块"""
        try:
            # 初始化时间录制器
            self.time_recorder = KeyTimeRecorder(str(self.db_path))

            # 初始化自动播放器
            self.auto_player = AutoPlayer(self, self.time_recorder)

            # 设置播放结束回调函数（将在播放控制器初始化后设置）

            # 初始化相关状态变量
            self.is_recording_timing = False
            self.is_auto_playing = False

            # 播放次数设定 - 自定义输入
            self.target_play_count = 5  # 默认5次
            # 注意：load_play_count_setting() 将在配置管理器初始化后调用

            # 同步播放次数设置到auto_player
            if self.auto_player:
                loop_enabled = self.target_play_count == -1
                self.auto_player.set_loop_mode(loop_enabled)
                if hasattr(self.auto_player, "target_play_count"):
                    self.auto_player.target_play_count = self.target_play_count
                print(f"同步播放次数设置到auto_player: {self.target_play_count}")

            # print("时间录制和自动播放模块初始化完成")

        except Exception as e:
            print(f"初始化keytime模块失败: {e}")
            # 创建空的替代对象，避免程序崩溃
            self.time_recorder = None
            self.auto_player = None
            self.is_recording_timing = False
            self.is_auto_playing = False

    def _init_keyframe_state(self):
        """初始化关键帧相关状态变量"""
        # 初始化关键帧管理器
        self.keyframe_manager = KeyframeManager(self)

        # 为了向后兼容，添加属性代理
        @property
        def current_keyframe_index(self):
            return self.keyframe_manager.current_keyframe_index

        @current_keyframe_index.setter
        def current_keyframe_index(self, value):
            self.keyframe_manager.current_keyframe_index = value

        @property
        def keyframe_markers(self):
            return self.keyframe_manager.keyframe_markers

        @keyframe_markers.setter
        def keyframe_markers(self, value):
            self.keyframe_manager.keyframe_markers = value

        # 绑定属性到实例
        self.__class__.current_keyframe_index = current_keyframe_index
        self.__class__.keyframe_markers = keyframe_markers

        # 添加更多属性代理
        @property
        def scroll_duration(self):
            return self.keyframe_manager.scroll_duration

        @scroll_duration.setter
        def scroll_duration(self, value):
            self.keyframe_manager.scroll_duration = value

        @property
        def is_loop_enabled(self):
            return self.keyframe_manager.is_loop_enabled

        @is_loop_enabled.setter
        def is_loop_enabled(self, value):
            self.keyframe_manager.is_loop_enabled = value

        # 绑定更多属性到实例
        self.__class__.scroll_duration = scroll_duration
        self.__class__.is_loop_enabled = is_loop_enabled

        # 添加方法代理
        self.add_keyframe = self.keyframe_manager.add_keyframe
        self.get_keyframes = self.keyframe_manager.get_keyframes
        self.delete_keyframe = self.keyframe_manager.delete_keyframe
        self.clear_keyframes = self.keyframe_manager.clear_keyframes
        self.clear_all_keyframes = self.keyframe_manager.clear_all_keyframes
        self.update_keyframe_order = self.keyframe_manager.update_keyframe_order
        self.add_current_keyframe = self.keyframe_manager.add_current_keyframe
        self.step_to_prev_keyframe = self.keyframe_manager.step_to_prev_keyframe
        self.step_to_next_keyframe = self.keyframe_manager.step_to_next_keyframe
        self.smooth_scroll_to = self.keyframe_manager.smooth_scroll_to

        # 添加项目管理器方法代理
        self.load_projects = self.project_manager.load_projects
        self.search_projects = self.project_manager.search_projects
        self.on_project_select = self.project_manager.on_project_select
        self.add_image_to_project = self.project_manager.add_image_to_project
        self.import_single_image = self.project_manager.import_single_image
        self.save_project_config = self.project_manager.save_project_config

        # 初始化原图模式管理器
        self.yuantu_manager = YuanTuManager(self)

        # 初始化投影管理器
        self.projection_manager = ProjectionManager(self)

        # 初始化图片处理器
        from image_processor import ImageProcessor
        self.image_processor = ImageProcessor(self)

        # 设置反色状态标志（在image_processor初始化后）
        self.is_inverted = False
        self.is_simple_inverted = False

    def init_scroll_engine(self):
        """初始化滚动引擎 - 需要在canvas和projection_manager都初始化后调用"""
        if hasattr(self, 'canvas') and hasattr(self, 'projection_manager'):
            # 使用新的GPU加速滚动引擎
            self.scroll_engine = VsyncOptimizedScrollEngine(self.canvas, self.projection_manager)
            pass
            
            # GPU支持检查
            if hasattr(self.scroll_engine, 'use_gpu') and self.scroll_engine.use_gpu:
                pass
            else:
                pass

    # UI组件属性代理已通过动态代理系统实现

    @property
    def screen_combo(self):
        return getattr(self, '_screen_combo', None)

    @screen_combo.setter
    def screen_combo(self, value):
        self._screen_combo = value

    # UI组件方法代理 - 现在代理到keyframe_manager
    def update_keyframe_indicators(self):
        """更新关键帧指示器 - 代理到关键帧管理器"""
        return self.keyframe_manager.update_keyframe_indicators()

    def update_preview_lines(self):
        """更新预览线 - 代理到关键帧管理器"""
        return self.keyframe_manager.update_preview_lines()

    def set_font_size(self, size):
        """设置字体大小 - 代理到UI组件"""
        return self.ui_components.set_font_size(size)

    def update_search_scope_options(self):
        """更新搜索范围选项 - 代理到UI组件"""
        return self.ui_components.update_search_scope_options()

    def clear_search_on_double_click(self, event):
        """双击搜索框时自动清空内容 - 代理到UI组件"""
        return self.ui_components.clear_search_on_double_click(event)

    def show_import_menu(self):
        """显示导入菜单 - 代理到UI组件"""
        return self.ui_components.show_import_menu()

    def show_font_menu(self):
        """显示字体菜单 - 代理到UI组件"""
        return self.ui_components.show_font_menu()

    def show_zoom_menu(self):
        """显示缩放菜单 - 代理到UI组件"""
        return self.ui_components.show_zoom_menu()

    def on_hover(self, button, entering):
        """处理按钮悬停效果 - 代理到UI组件"""
        return self.ui_components.on_hover(button, entering)

    @property
    def project_tree(self):
        return self.ui_components.project_tree

    @property
    def search_entry(self):
        return self.ui_components.search_entry

    @property
    def search_var(self):
        return self.ui_components.search_var

    @property
    def search_scope_var(self):
        return self.ui_components.search_scope_var

    @property
    def search_scope_combo(self):
        return self.ui_components.search_scope_combo

    @property
    def btn_import(self):
        return self.ui_components.btn_import

    @property
    def btn_show(self):
        return self.ui_components.btn_show

    @property
    def btn_sync(self):
        return self.ui_components.btn_sync

    @property
    def btn_top(self):
        return self.ui_components.btn_top

    @property
    def btn_original(self):
        return self.ui_components.btn_original

    @property
    def btn_zoom(self):
        return self.ui_components.btn_zoom

    @property
    def btn_invert(self):
        return self.ui_components.btn_invert

    @property
    def btn_simple_invert(self):
        return self.ui_components.btn_simple_invert

    @property
    def btn_font(self):
        return self.ui_components.btn_font

    @property
    def import_menu(self):
        return self.ui_components.import_menu

    @property
    def font_menu(self):
        return self.ui_components.font_menu

    @property
    def zoom_menu(self):
        return self.ui_components.zoom_menu

    @property
    def context_menu(self):
        return self.ui_components.context_menu

    @property
    def indicator_frame(self):
        return self.ui_components.indicator_frame

    @property
    def preview_line(self):
        return self.ui_components.preview_line

    @property
    def next_keyframe_line(self):
        return self.ui_components.next_keyframe_line

    # ==================== 图片处理器代理属性和方法 ====================

    @property
    def image(self):
        """当前图片代理属性"""
        return self.image_processor.current_image

    @image.setter
    def image(self, value):
        """当前图片设置代理"""
        self.image_processor.current_image = value

    @property
    def photo(self):
        """当前PhotoImage代理属性"""
        return self.image_processor.current_photo

    @photo.setter
    def photo(self, value):
        """当前PhotoImage设置代理"""
        self.image_processor.current_photo = value

    @property
    def image_cache(self):
        """图片缓存代理属性"""
        return self.image_processor.image_cache

    @property
    def image_on_canvas(self):
        """画布图片代理属性"""
        return self.image_processor.image_on_canvas

    @image_on_canvas.setter
    def image_on_canvas(self, value):
        """画布图片设置代理"""
        self.image_processor.image_on_canvas = value

    @property
    def is_inverted(self):
        """黄字效果状态代理属性"""
        return self.image_processor.is_inverted

    @is_inverted.setter
    def is_inverted(self, value):
        """黄字效果状态设置代理"""
        self.image_processor.is_inverted = value

    @property
    def is_simple_inverted(self):
        """简单反色状态代理属性"""
        return self.image_processor.is_simple_inverted

    @is_simple_inverted.setter
    def is_simple_inverted(self, value):
        """简单反色状态设置代理"""
        self.image_processor.is_simple_inverted = value

    @property
    def zoom_ratio(self):
        """缩放比例代理属性"""
        return self.image_processor.zoom_ratio

    @zoom_ratio.setter
    def zoom_ratio(self, value):
        """缩放比例设置代理"""
        self.image_processor.zoom_ratio = value

    @property
    def zoom_step(self):
        """缩放步长代理属性"""
        return self.image_processor.zoom_step

    @zoom_step.setter
    def zoom_step(self, value):
        """缩放步长设置代理"""
        self.image_processor.zoom_step = value

    # 图片处理方法代理
    def load_image(self, path):
        """加载图片"""
        return self.image_processor.load_image(path)

    def update_image(self):
        """更新图片显示"""
        return self.image_processor.update_image()

    def apply_yellow_text_effect(self, image):
        """应用黄字效果"""
        return self.image_processor.apply_yellow_text_effect(image)

    def simple_invert_image(self, image):
        """简单反色"""
        return self.image_processor.simple_invert_image(image)

    def toggle_invert(self):
        """切换黄字效果"""
        return self.image_processor.toggle_invert()

    def toggle_simple_invert(self):
        """切换简单反色"""
        return self.image_processor.toggle_simple_invert()

    def save_effect_image(self):
        """保存效果图片（变色/反色）"""
        return self.image_processor.save_effect_image()

    def on_scroll(self, *args):
        """滚动处理"""
        return self.image_processor.on_scroll(*args)

    def on_scroll_end(self):
        """滚动结束处理"""
        return self.image_processor.on_scroll_end()

    def zoom_in(self):
        """放大"""
        return self.image_processor.zoom_in()

    def zoom_out(self):
        """缩小"""
        return self.image_processor.zoom_out()

    def zoom_reset(self):
        """重置缩放"""
        return self.image_processor.zoom_reset()

    def clear_current_image(self):
        """清除当前图片"""
        return self.image_processor.clear_current_image()

    def _should_auto_jump_to_first(self):
        """判断是否应该智能跳转到第一帧"""
        if not hasattr(self, "current_image_id"):
            return False

        # 如果没有时间录制器，使用简单的跳转逻辑
        if not self.time_recorder:
            return True  # 允许跳转

        # 检查是否有录制的时间数据
        if not self.time_recorder.has_timing_data(self.current_image_id):
            return True  # 没有录制数据时也允许跳转

        # 分析操作历史，判断用户是否完成了一个完整的浏览周期
        if len(self.keyframe_operation_history) < 3:  # 至少需要几个操作
            return False

        # 检查最近的操作模式
        recent_ops = (
            self.keyframe_operation_history[-4:]
            if len(self.keyframe_operation_history) >= 4
            else self.keyframe_operation_history[-3:]
        )

        # 统计最近操作中的向前和向后次数
        forward_count = sum(1 for op in recent_ops if op["type"] == "next")
        backward_count = sum(1 for op in recent_ops if op["type"] == "prev")

        # 如果有明显的来回切换模式，不跳转
        if backward_count > 0 and forward_count > 0:
            print(
                f"检测到来回切换模式 (前进:{forward_count}, 后退:{backward_count})，不跳转"
            )
            return False

        # 如果最近主要是连续向前操作，说明用户在顺序浏览，可以自动跳转
        jump_decision = forward_count >= 3 and backward_count == 0
        print(
            f"跳转判断: 前进{forward_count}次, 后退{backward_count}次 -> {'跳转' if jump_decision else '不跳转'}"
        )
        return jump_decision

    def _should_auto_jump_to_last(self):
        """判断是否应该智能跳转到最后一帧"""
        # 向前跳转的条件更严格，需要用户明确的反向浏览意图
        if len(self.keyframe_operation_history) < 2:
            return False

        # 检查最近的操作是否是连续向前的
        recent_ops = self.keyframe_operation_history[-2:]
        backward_count = sum(1 for op in recent_ops if op["type"] == "prev")

        # 如果最近都是向前操作，可以跳转
        return backward_count >= 2

    def _schedule_jump_check(self):
        """安排延迟跳转检查"""
        # 取消之前的跳转检查
        if hasattr(self, "_jump_check_timer"):
            self.root.after_cancel(self._jump_check_timer)

        # 2秒后检查是否应该跳转
        self._jump_check_timer = self.root.after(2000, self._check_and_jump)

    def _check_and_jump(self):
        """检查并执行跳转"""
        import time

        current_time = time.time()

        # 检查是否在等待期间有新的操作
        if current_time - self.last_operation_time >= 1.8:  # 允许一些时间误差
            # 记录最后一帧的停留时间（仅在录制时）
            if (
                self.is_recording_timing
                and self.time_recorder
                and hasattr(self, "current_image_id")
                and self.current_keyframe_index >= 0
            ):

                keyframes = self.get_keyframes(self.current_image_id)
                if keyframes and self.current_keyframe_index < len(keyframes):
                    current_keyframe_id = keyframes[self.current_keyframe_index][0]
                    self.time_recorder.record_keyframe_timing(current_keyframe_id)
                    print(f"记录最后一帧 {self.current_keyframe_index + 1} 的停留时间")

            # 检查是否应该智能跳转到第一帧
            if self._should_auto_jump_to_first():
                print("智能跳转到第一帧开始播放")
                self.current_keyframe_index = 0

                # 获取关键帧并跳转
                keyframes = self.get_keyframes(self.current_image_id)
                if keyframes:
                    _, target_position, _ = keyframes[0]
                    # 直接跳转到第一帧
                    self.canvas.yview_moveto(target_position)
                    if self.second_window and self.sync_enabled:
                        self.sync_projection_screen_absolute()

                    # 更新指示器和预览线
                    self.update_keyframe_indicators()
                    self.update_preview_lines()

                    # 确保投影窗口也更新
                    if self.second_window and self.sync_enabled:
                        self.update_projection()

                # 清除操作历史，准备新的播放周期
                self.keyframe_operation_history = []
            else:
                print("用户可能在最后一帧练习，不自动跳转")

    def update_countdown_display(self, remaining_time):
        """更新倒计时显示（正常播放时）

        Args:
            remaining_time: 剩余时间（秒）
        """
        # 计算已播放时间
        played_time = 0.0
        if (hasattr(self.auto_player, 'current_frame_start_time') and
            self.auto_player.current_frame_start_time and
            hasattr(self.auto_player, 'current_frame_duration')):
            played_time = self.auto_player.current_frame_duration - remaining_time

        # 只更新"倒"参数标签
        if hasattr(self, 'countdown_dao_label'):
            dao_text = f"倒: {remaining_time:.1f}秒" if remaining_time > 0 else "倒: --"
            self.countdown_dao_label.config(text=dao_text)

    def update_pause_countdown_display(self, pause_time, remaining_time=None, played_time=None):
        """更新暂停期间的倒计时显示

        Args:
            pause_time: 暂停累计时间（秒）
            remaining_time: 暂停时的剩余时间（秒）
            played_time: 已播放时间（秒）
        """
        # 只更新"倒"参数标签
        if hasattr(self, 'countdown_dao_label'):
            dao_text = f"倒: {remaining_time:.1f}秒" if remaining_time is not None else "倒: --"
            self.countdown_dao_label.config(text=dao_text)

    def show_script_info(self):
        """显示和编辑脚本信息"""
        if not hasattr(self, "current_image_id"):
            messagebox.showinfo("提示", "请先选择一张图片")
            return

        if not self.time_recorder:
            messagebox.showerror("错误", "时间录制模块未初始化")
            return

        # 检查是否为原图模式
        if self.original_mode and hasattr(self, "yuantu_manager"):
            # 原图模式脚本
            self._show_original_mode_script_info()
        else:
            # 普通关键帧脚本
            self._show_normal_script_info()

    def _show_normal_script_info(self):
        """显示普通关键帧脚本信息"""
        # 获取当前图片的时间数据
        timing_sequence = self.time_recorder.get_timing_sequence(self.current_image_id)
        if not timing_sequence:
            messagebox.showinfo("提示", "当前图片没有录制的时间数据")
            return

        # 创建脚本信息窗口
        self._create_script_info_window(timing_sequence)

    def _show_original_mode_script_info(self):
        """显示原图模式脚本信息"""
        # 获取原图模式时间数据
        timing_sequence = self.time_recorder.get_original_mode_timing_sequence(
            self.current_image_id
        )
        if not timing_sequence:
            messagebox.showinfo("提示", "当前图片没有录制的原图模式时间数据")
            return

        # 获取相似图片列表
        similar_images = self.yuantu_manager.similar_images
        if not similar_images:
            messagebox.showinfo("提示", "当前图片没有相似图片")
            return

        # 获取标记类型
        mark_type = "loop"  # 默认循环模式
        if hasattr(self, "current_image_id"):
            # 检查图片本身的标记类型
            img_mark_type = self.yuantu_manager.get_original_mark_type(
                "image", self.current_image_id
            )
            if img_mark_type:
                mark_type = img_mark_type
            else:
                # 检查图片所在文件夹的标记类型
                result = self.safe_db_execute(
                    "SELECT folder_id FROM images WHERE id = ?",
                    (self.current_image_id,),
                    fetch="one",
                )
                if result and result[0]:
                    folder_mark_type = self.yuantu_manager.get_original_mark_type(
                        "folder", result[0]
                    )
                    if folder_mark_type:
                        mark_type = folder_mark_type

        # 生成原图模式脚本内容
        from keytime import (
            format_original_mode_script_for_edit,
            generate_original_mode_script,
        )

        script_content = generate_original_mode_script(
            self.current_image_id, timing_sequence, similar_images, mark_type
        )
        edit_content = format_original_mode_script_for_edit(
            timing_sequence, similar_images
        )

        # 创建原图模式脚本信息窗口
        self._create_original_mode_script_info_window(
            timing_sequence, similar_images, script_content, edit_content, mark_type
        )

    def _create_script_info_window(self, timing_sequence):
        """创建脚本信息编辑窗口"""
        # 创建新窗口
        script_window = tk.Toplevel(self.root)
        script_window.title("脚本信息编辑")
        script_window.geometry("600x600")
        script_window.transient(self.root)
        script_window.grab_set()

        # 创建主框架
        main_frame = tk.Frame(script_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 标题
        title_label = tk.Label(
            main_frame, text="关键帧时间脚本", font=(self.default_font, 14, "bold")
        )
        title_label.pack(pady=(0, 10))

        # 说明文字
        info_label = tk.Label(
            main_frame,
            text="格式: 帧序号 时间(秒) | 可以修改时间值并保存",
            font=(self.default_font, 10),
        )
        info_label.pack(pady=(0, 10))

        # 保存按钮（移到上方）
        save_btn = tk.Button(
            main_frame,
            text="保存修改",
            command=lambda: self._save_script_changes(
                script_text, script_window, timing_sequence
            ),
            bg="#ccffcc",
            font=(self.default_font, 10),
        )
        save_btn.pack(pady=(0, 10))

        # 创建滚动文本框
        text_frame = tk.Frame(main_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)

        # 文本框和滚动条
        text_scroll = tk.Scrollbar(text_frame)
        text_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        script_text = tk.Text(
            text_frame,
            yscrollcommand=text_scroll.set,
            font=(self.default_font, 11),
            wrap=tk.WORD,
        )
        script_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        text_scroll.config(command=script_text.yview)

        # 填充脚本内容
        script_content = self._format_script_content(timing_sequence)
        script_text.insert(tk.END, script_content)

        # 应用当前字体设置
        self._apply_font_to_script_window(
            script_window, title_label, info_label, save_btn, script_text
        )

        # 使窗口居中
        script_window.update_idletasks()
        x = (script_window.winfo_screenwidth() // 2) - (
            script_window.winfo_width() // 2
        )
        y = (script_window.winfo_screenheight() // 2) - (
            script_window.winfo_height() // 2
        )
        script_window.geometry(f"+{x}+{y}")

    def _format_script_content(self, timing_sequence):
        """格式化脚本内容"""
        # 获取关键帧映射（ID到帧号）
        keyframe_mapping = self._get_keyframe_mapping()

        content = ""
        for i, (keyframe_id, duration, sequence_order) in enumerate(timing_sequence):
            # 获取关键帧的实际帧号（1,2,3,4,5）
            frame_number = keyframe_mapping.get(keyframe_id, "?")
            content += f"{frame_number}  {duration:6.1f}s\n"

        return content

    def _get_keyframe_mapping(self):
        """获取关键帧ID到帧号的映射"""
        if not hasattr(self, "current_image_id"):
            return {}

        keyframes = self.get_keyframes(self.current_image_id)
        mapping = {}
        for i, (keyframe_id, position, order) in enumerate(keyframes):
            mapping[keyframe_id] = i + 1  # 帧号从1开始

        return mapping

    def _apply_font_to_script_window(
        self, script_window, title_label, info_label, save_btn, script_text
    ):
        """应用字体设置到脚本窗口"""
        try:
            # 更新标题字体
            title_label.configure(
                font=(self.default_font, self.large_font_size, "bold")
            )

            # 更新说明文字字体
            info_label.configure(font=(self.default_font, self.menu_font_size))

            # 更新保存按钮字体
            save_btn.configure(font=(self.default_font, self.menu_font_size))

            # 更新文本框字体
            script_text.configure(font=(self.default_font, self.font_size))

        except Exception as e:
            print(f"应用脚本窗口字体失败: {e}")

    def _save_script_changes(self, script_text, script_window, original_timing):
        """保存脚本修改"""
        try:
            # 获取文本内容
            content = script_text.get("1.0", tk.END)
            lines = content.strip().split("\n")

            # 解析修改后的时间数据
            new_timings = []
            line_num = 0

            for line in lines:
                line = line.strip()
                if not line or line.startswith("#"):
                    continue

                # 解析格式: "1  10.0s" 或 "1 10.0"
                parts = line.split()
                if len(parts) >= 2:
                    try:
                        _ = int(parts[0])  # frame_num - 验证格式但不使用
                        time_str = parts[1].rstrip("s")
                        duration = float(time_str)

                        if duration < 0:
                            raise ValueError("时间不能为负数")

                        # 使用原始的keyframe_id和sequence_order
                        if line_num < len(original_timing):
                            original_keyframe_id = original_timing[line_num][0]
                            original_sequence_order = original_timing[line_num][2]
                            new_timings.append(
                                (
                                    original_keyframe_id,
                                    duration,
                                    original_sequence_order,
                                )
                            )

                        line_num += 1

                    except (ValueError, IndexError):
                        messagebox.showerror(
                            "格式错误",
                            f"第{line_num + 1}行格式错误: {line}\n请使用格式: 帧序号 时间",
                        )
                        return

            if not new_timings:
                messagebox.showerror("错误", "没有找到有效的时间数据")
                return

            if len(new_timings) != len(original_timing):
                result = messagebox.askyesno(
                    "确认修改",
                    f"原始数据有{len(original_timing)}帧，新数据有{len(new_timings)}帧\n是否继续保存？",
                )
                if not result:
                    return

            # 保存到数据库
            if self._update_timing_data(new_timings):
                messagebox.showinfo("保存成功", "脚本信息已更新")
                script_window.destroy()
                # 更新按钮状态
                self.update_button_status()
            else:
                messagebox.showerror("保存失败", "无法保存脚本信息到数据库")

        except Exception as e:
            messagebox.showerror("错误", f"保存脚本时发生错误: {e}")

    def _update_timing_data(self, new_timings):
        """更新数据库中的时间数据（优化版本：使用事务批处理）"""
        try:
            # 准备批量操作
            operations = [
                {
                    "query": "DELETE FROM keyframe_timings WHERE image_id = ?",
                    "params": (self.current_image_id,),
                }
            ]

            # 添加插入操作
            for keyframe_id, duration, sequence_order in new_timings:
                operations.append(
                    {
                        "query": """INSERT INTO keyframe_timings
                               (image_id, keyframe_id, duration, sequence_order)
                               VALUES (?, ?, ?, ?)""",
                        "params": (
                            self.current_image_id,
                            keyframe_id,
                            duration,
                            sequence_order,
                        ),
                    }
                )

            # 使用数据库管理器的批量操作
            success = self.db_manager.execute_batch(operations)

            if success:
                print(
                    f"已更新图片 {self.current_image_id} 的时间数据，共 {len(new_timings)} 条记录"
                )

            return success

        except Exception as e:
            print(f"更新时间数据失败: {e}")
            return False

    def _create_original_mode_script_info_window(
        self, timing_sequence, similar_images, script_content, edit_content, mark_type
    ):
        """创建原图模式脚本信息编辑窗口"""
        # 创建新窗口
        script_window = tk.Toplevel(self.root)
        script_window.title("原图模式脚本信息编辑")
        script_window.geometry("800x700")
        script_window.transient(self.root)
        script_window.grab_set()

        # 创建主框架
        main_frame = tk.Frame(script_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 标题
        title_label = tk.Label(
            main_frame, text="原图模式时间脚本", font=(self.default_font, 14, "bold")
        )
        title_label.pack(pady=(0, 10))

        # 说明文字
        info_label = tk.Label(
            main_frame,
            text=f"标记类型: {mark_type} | 相似图片数量: {len(similar_images)} | 可以修改时间值并保存",
            font=(self.default_font, 10),
        )
        info_label.pack(pady=(0, 10))

        # 保存按钮
        save_btn = tk.Button(
            main_frame,
            text="保存修改",
            command=lambda: self._save_original_mode_script_changes(
                script_text, script_window, timing_sequence, similar_images
            ),
            bg="#ccffcc",
            font=(self.default_font, 10),
        )
        save_btn.pack(pady=(0, 10))

        # 创建滚动文本框
        text_frame = tk.Frame(main_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)

        # 文本框和滚动条
        text_scroll = tk.Scrollbar(text_frame)
        text_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        script_text = tk.Text(
            text_frame,
            yscrollcommand=text_scroll.set,
            font=(self.default_font, 11),
            wrap=tk.WORD,
        )
        script_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        text_scroll.config(command=script_text.yview)

        # 填充脚本内容
        script_text.insert(tk.END, edit_content)

        # 应用当前字体设置
        self._apply_font_to_script_window(
            script_window, title_label, info_label, save_btn, script_text
        )

        # 使窗口居中
        script_window.update_idletasks()
        x = (script_window.winfo_screenwidth() // 2) - (
            script_window.winfo_width() // 2
        )
        y = (script_window.winfo_screenheight() // 2) - (
            script_window.winfo_height() // 2
        )
        script_window.geometry(f"+{x}+{y}")

    def _save_original_mode_script_changes(
        self, script_text, script_window, original_timing, similar_images
    ):
        """保存原图模式脚本修改"""
        try:
            # 获取修改后的内容
            new_content = script_text.get("1.0", tk.END).strip()

            # 解析新的时间序列
            from keytime import (
                parse_original_mode_script,
                validate_original_mode_timing_sequence,
            )

            new_timings = parse_original_mode_script(new_content)

            # 验证时间序列
            if not validate_original_mode_timing_sequence(new_timings, similar_images):
                messagebox.showerror("验证失败", "时间序列验证失败，请检查格式是否正确")
                return

            # 确认保存
            result = messagebox.askyesno(
                "确认保存",
                f"确定要保存修改后的时间序列吗？\n原序列长度: {len(original_timing)}\n新序列长度: {len(new_timings)}",
            )

            if result:
                # 更新数据库中的时间数据
                if self._update_original_mode_timing_data(new_timings):
                    messagebox.showinfo("保存成功", "原图模式时间序列已更新")
                    script_window.destroy()
                    # 更新按钮状态
                    self.update_button_status()
                else:
                    messagebox.showerror("保存失败", "无法保存原图模式脚本信息到数据库")

        except Exception as e:
            messagebox.showerror("错误", f"保存原图模式脚本时发生错误: {e}")

    def _update_original_mode_timing_data(self, new_timings):
        """更新数据库中的原图模式时间数据（优化版本：使用事务批处理）"""
        try:
            # 准备批量操作
            operations = [
                {
                    "query": "DELETE FROM original_mode_timings WHERE base_image_id = ?",
                    "params": (self.current_image_id,),
                }
            ]

            # 添加插入操作
            for i, (from_image_id, to_image_id, duration) in enumerate(new_timings):
                operations.append(
                    {
                        "query": """INSERT INTO original_mode_timings
                               (base_image_id, from_image_id, to_image_id, duration, sequence_order, mark_type)
                               VALUES (?, ?, ?, ?, ?, ?)""",
                        "params": (
                            self.current_image_id,
                            from_image_id,
                            to_image_id,
                            duration,
                            i,
                            "loop",
                        ),
                    }
                )

            # 使用数据库管理器的批量操作
            success = self.db_manager.execute_batch(operations)

            if success:
                print(
                    f"已更新图片 {self.current_image_id} 的原图模式时间数据，共 {len(new_timings)} 条记录"
                )

            return success

        except Exception as e:
            print(f"更新原图模式时间数据失败: {e}")
            return False

    def update_button_status(self):
        """更新按钮状态显示"""
        # 更新播放次数按钮显示
        if hasattr(self, "target_play_count"):
            self._update_play_count_button()

        # 检查是否有选中的图片和时间数据
        has_current_image = hasattr(self, "current_image_id")

        # 根据模式检查时间数据
        has_timing_data = False
        if has_current_image and self.time_recorder:
            if self.original_mode and hasattr(self, "yuantu_manager"):
                # 原图模式：检查原图模式时间数据
                has_timing_data = self.time_recorder.has_original_mode_timing_data(
                    self.current_image_id
                )
            else:
                # 普通模式：检查关键帧时间数据
                has_timing_data = self.time_recorder.has_timing_data(
                    self.current_image_id
                )

        # 禁用/启用相关按钮
        if self.is_recording_timing:
            # 录制时禁用播放、清除和脚本
            self.btn_play.config(state="disabled")
            self.btn_clear_timing.config(state="disabled")
            self.btn_script_info.config(state="disabled")
        elif self.is_auto_playing:
            # 播放时禁用录制、清除和脚本
            self.btn_record.config(state="disabled")
            self.btn_clear_timing.config(state="disabled")
            self.btn_script_info.config(state="disabled")
        else:
            # 都不在进行时的状态控制
            self.btn_record.config(state="normal")

            # 播放按钮：只有有时间数据时才能播放，并确保颜色正确
            if has_timing_data:
                self.btn_play.config(state="normal", bg="#f0f0f0")  # 确保停止时颜色正确
            else:
                self.btn_play.config(state="disabled", bg="#f0f0f0")

            # 清除时间按钮：只有有时间数据时才能清除
            if has_timing_data:
                self.btn_clear_timing.config(state="normal")
            else:
                self.btn_clear_timing.config(state="disabled")

            # 脚本信息按钮：只有有时间数据时才能查看
            if has_timing_data:
                self.btn_script_info.config(state="normal")
            else:
                self.btn_script_info.config(state="disabled")

    def show_contact_info(self):
        """显示联系方式信息"""
        contact_info = "联系人: 静等\n电话: 18162074638"
        messagebox.showinfo("作者", contact_info)

    def clear_current_keyframes(self):
        """清除当前图片的所有关键帧"""
        if not hasattr(self, "current_image_id"):
            messagebox.showinfo("提示", "请先选择一张图片")
            return

        if messagebox.askyesno("确认", "确定要清除所有关键帧吗？"):
            if self.clear_keyframes(self.current_image_id):
                self.current_keyframe_index = -1
                self.update_keyframe_indicators()
                messagebox.showinfo("成功", "清除关键帧成功")
            else:
                messagebox.showerror("错误", "清除关键帧失败")



    def import_folder(self):
        """导入整个文件夹（使用project_db模块）"""
        folder_path = filedialog.askdirectory()
        if folder_path:
            try:
                # 使用project_db模块进行数据库操作
                result = self.project_db.import_folder_to_db(folder_path, self.get_sort_key)

                if result[0] is None:  # folder_id为None表示没有找到图片文件
                    messagebox.showinfo("提示", "所选文件夹中没有支持的图片文件")
                    return

                folder_id, new_images, existing_images = result

                if not new_images and existing_images:
                    messagebox.showinfo("提示", "所有图片都已存在")
                    return

                # 显示进度条
                progress = tk.Toplevel(self.root)
                progress.title("导入进度")
                progress.geometry("300x150")
                progress.transient(self.root)

                label = ttk.Label(
                    progress,
                    text="正在导入图片...",
                    font=("Microsoft YaHei UI", 10),
                )
                label.pack(pady=10)

                progress_bar = ttk.Progressbar(
                    progress, length=200, mode="determinate"
                )
                progress_bar.pack(pady=10)

                count_label = ttk.Label(
                    progress,
                    text="0/" + str(len(new_images)),
                    font=("Microsoft YaHei UI", 10),
                )
                count_label.pack(pady=10)

                progress.update()

                # 🔧 优化：移除不必要的延迟，直接显示完成状态
                progress_bar["value"] = 100
                count_label["text"] = f"{len(new_images)}/{len(new_images)}"
                progress.update()

                progress.destroy()

                # 更新项目树
                self.load_projects()

                # 🔧 修复：更新搜索范围选项，显示新导入的文件夹
                self.update_search_scope_options()

            except Exception as e:
                print(f"导入失败: {e}")
                messagebox.showerror("错误", f"导入失败: {e}")

    def get_sort_key(self, filename):
        """获取排序键值
        支持多种文件名格式：
        1. "001.圣哉三一1" - 前缀数字.中文后缀数字
        2. "10想起你" - 开头数字+中文
        3. "因为有你01" - 中文数字
        排序优先级：前缀数字 > 中文拼音 > 后缀数字
        """
        import re

        # 移除扩展名
        name = Path(filename).stem

        # 初始化排序键组件
        prefix_number = 0
        text_part = name
        suffix_number = 0

        # 模式1: 匹配 "001.圣哉三一1" 格式（前缀数字.中文后缀数字）
        pattern1 = re.match(r"^(\d+)\.(.+?)(\d+)$", name)
        if pattern1:
            prefix_number = int(pattern1.group(1))
            text_part = pattern1.group(2)
            suffix_number = int(pattern1.group(3))
        else:
            # 模式2: 匹配 "10想起你" 格式（开头数字+中文）
            pattern2 = re.match(r"^(\d+)(.+)$", name)
            if pattern2:
                prefix_number = int(pattern2.group(1))
                text_part = pattern2.group(2)
                suffix_number = 0  # 没有后缀数字
            else:
                # 模式3: 匹配 "因为有你_01" 或 "因为有你01" 格式（中文_数字或中文数字）
                pattern3 = re.search(r"^(.+?)_?(\d+)$", name)
                if pattern3:
                    text_part = pattern3.group(1).replace("_", "")  # 移除下划线
                    suffix_number = int(pattern3.group(2))
                    prefix_number = 0  # 没有前缀数字
                else:
                    # 模式4: 纯文本，没有数字
                    text_part = name
                    prefix_number = 0
                    suffix_number = 0

        # 获取中文的拼音首字母
        pinyin_part = ""
        if text_part:
            pinyin_part = "".join(
                pypinyin.lazy_pinyin(text_part, style=pypinyin.FIRST_LETTER)
            )

        # 排序键：(前缀数字, 中文拼音, 后缀数字)
        return (prefix_number, pinyin_part.lower(), suffix_number)

    def reset_view(self):
        """返回顶部"""
        # 主画布返回顶部
        self.canvas.yview_moveto(0)
        # 只在同步开启时更副屏，使用绝对位置同步
        if self.second_canvas and self.sync_enabled:
            self.sync_projection_screen_absolute()
            self.second_window.update()

    def reset_zoom(self):
        """重置图片原始大小"""
        if self.image:
            self.zoom_ratio = 1.0
            self.update_image()
            # 只在同步开启时更新副屏
            if self.second_window and self.sync_enabled:
                self.update_second_screen()

    def handle_drop(self, event):
        """拖放功能已禁用"""
        return  # 直接返回，不处理拖放
        # 以下代码已禁用
        """修改拖放处理方法，支持批量拖拽添加到项目列表"""
        try:
            # tkinterdnd2 通过 event.data 传递文件列表，需要解析
            files_data = event.data

            # 处理文件路径，可能包含空格的路径会用花括号包围
            import re
            # 匹配被花括号包围的路径或者普通路径
            file_pattern = r'\{([^}]+)\}|(\S+)'
            matches = re.findall(file_pattern, files_data)
            files = [match[0] if match[0] else match[1] for match in matches]

            valid_files = []
            invalid_files = []

            for file_path in files:
                # 清理路径中的多余字符
                file_path = file_path.strip()
                if not file_path:
                    continue

                # 将字节串转换为字符串（如果需要）
                if isinstance(file_path, bytes):
                    file_path = file_path.decode("utf-8")  # 使用 UTF-8 编码

                # 检查文件类型
                if file_path.lower().endswith(
                    (".jpg", ".jpeg", ".png", ".bmp", ".gif", ".tif")
                ):
                    valid_files.append(file_path)
                else:
                    invalid_files.append(file_path)

            # 批量添加有效的图片文件
            if valid_files:
                # 批量添加，除了最后一个文件，其他都不立即更新列表
                for i, file_path in enumerate(valid_files[:-1]):
                    self.root.after(
                        10 + i * 5,
                        lambda f=file_path: self.add_image_to_project(
                            f, update_list=False
                        ),
                    )

                # 最后一个文件添加完成后更新项目列表
                if valid_files:
                    last_file = valid_files[-1]
                    delay = 10 + (len(valid_files) - 1) * 5
                    self.root.after(
                        delay,
                        lambda f=last_file: self.add_image_to_project(
                            f, update_list=True
                        ),
                    )

            # 如果有无效文件，显示错误信息
            if invalid_files:
                invalid_count = len(invalid_files)
                valid_count = len(valid_files)
                if valid_count > 0:
                    message = f"成功添加 {valid_count} 个图片文件，{invalid_count} 个文件格式不支持"
                else:
                    message = "所选文件格式不支持，请选择图片文件"
                self.root.after(10, lambda: messagebox.showwarning("提示", message))

        except Exception as e:
            print(f"拖放处理错误: {e}")



    def _select_image_in_tree(self, image_id):
        """安全地在项目树中选中图片"""
        try:
            # 检查项目是否存在于树中
            if self.project_tree.exists(image_id):
                self.project_tree.selection_set(image_id)
                self.project_tree.see(image_id)
            else:
                print(f"项目树中未找到图片ID: {image_id}")
        except Exception as e:
            print(f"选中图片失败: {e}")

    # update_projection 方法已迁移到 projection_manager

    def update_screen_list(self):
        """更新屏幕列表"""
        # 检查projection_manager是否已初始化
        if not hasattr(self, 'projection_manager') or not self.projection_manager:
            print("投影管理器未初始化，跳过屏幕列表更新")
            return

        screen_list = []
        for i, screen in enumerate(self.projection_manager.screens):

            is_primary = screen.x == 0 and screen.y == 0
            screen.is_primary = is_primary

            display_name = str(i + 1)
            if is_primary:
                display_name += "主"
            screen_list.append(display_name)

        if not screen_list:
            screen_list = ["1"]

        # 检查screen_combo是否存在（UI组件可能还未创建）
        if self.screen_combo is not None:
            self.screen_combo["values"] = screen_list
            # 默认选择第一个非主显示器
            for i, screen in enumerate(self.projection_manager.screens):
                if not screen.is_primary:
                    self.screen_combo.current(i)
                    break
        else:
            # UI组件还未创建，保存screen_list供后续使用
            self._pending_screen_list = screen_list

    def reorganize_projects(self, conn):
        """重新整理项目ID和序号（跳过手动排序的文件夹）"""
        try:
            # 重新整理非文件夹图片的顺序
            cursor = conn.execute(
                """
                SELECT id FROM images
                WHERE folder_id IS NULL
                ORDER BY order_index
            """
            )
            for index, (id,) in enumerate(cursor.fetchall()):
                conn.execute(
                    "UPDATE images SET order_index = ? WHERE id = ?", (index, id)
                )

            # 重新整理文件夹中图片的顺序（跳过手动排序的文件夹）
            cursor = conn.execute("SELECT id FROM folders")
            for (folder_id,) in cursor.fetchall():
                # 检查是否为手动排序文件夹
                manual_cursor = conn.execute(
                    """
                    SELECT is_manual_sort FROM manual_sort_folders
                    WHERE folder_id = ?
                """,
                    (folder_id,),
                )
                manual_result = manual_cursor.fetchone()
                is_manual_sort = manual_result and manual_result[0]

                if not is_manual_sort:
                    # 只对非手动排序的文件夹重新整理顺序
                    sub_cursor = conn.execute(
                        """
                        SELECT id FROM images
                        WHERE folder_id = ?
                        ORDER BY order_index
                    """,
                        (folder_id,),
                    )
                    for index, (id,) in enumerate(sub_cursor.fetchall()):
                        conn.execute(
                            "UPDATE images SET order_index = ? WHERE id = ?",
                            (index, id),
                        )

            conn.commit()
        except Exception as e:
            print(f"重新整理项目失败: {e}")
            raise e

    def get_window_screen(self):
        """获取当前窗口所在的屏幕"""
        try:
            import win32gui

            hwnd = self.root.winfo_id()
            monitor = win32gui.MonitorFromWindow(
                hwnd, win32gui.MONITOR_DEFAULTTONEAREST
            )
            info = win32gui.GetMonitorInfo(monitor)

            # 检查是否是主显示器
            is_primary = bool(info["Flags"] & 1)  # MONITORINFOF_PRIMARY = 1

            # 根据监视器信息匹配screens列表中的屏幕
            for screen in self.screens:
                if (
                    screen.x == info["Monitor"][0]
                    and screen.y == info["Monitor"][1]
                    and screen.width == info["Monitor"][2] - info["Monitor"][0]
                    and screen.height == info["Monitor"][3] - info["Monitor"][1]
                ):
                    # 为screen对象添加主显示器标识
                    screen.is_primary = is_primary
                    return screen

        except Exception as e:
            print(f"获取窗口所屏幕失败: {e}")
        return None

    def set_original_display_mode(self, mode):
        """设置原图显示模式（从画布右键菜单调用）"""
        try:
            if mode == "fit":
                self.original_display_mode = 'fit'
            elif mode == "stretch":
                self.original_display_mode = 'stretch'

            # 更新显示（如果原图模式已启用）
            if self.original_mode and hasattr(self, 'current_image_id') and self.image:
                self.update_image()
                if self.second_window:
                    self.update_projection()

            # 保存配置
            self.config_manager.save_config()

            # 保存显示模式设置到数据库
            self.save_display_mode_settings()

        except Exception as e:
            print(f"设置原图显示模式失败: {e}")

    def toggle_original_mode(self):
        """切换原图模式（保持向后兼容）"""
        self.yuantu_manager.toggle_original_mode()

    def zoom_reset_wrapper(self):
        """还原缩放包装函数"""
        self.zoom_reset()
        # 延迟重新显示菜单，确保当前操作完成
        self.root.after(50, self.show_zoom_menu)

    def zoom_in_wrapper(self):
        """放大包装函数"""
        self.zoom_in()
        # 延迟重新显示菜单，确保当前操作完成
        self.root.after(50, self.show_zoom_menu)

    def zoom_out_wrapper(self):
        """缩小包装函数"""
        self.zoom_out()
        # 延迟重新显示菜单，确保当前操作完成
        self.root.after(50, self.show_zoom_menu)

    def sync_all_folders(self):
        """同步所有文件夹内容（使用project_db模块）"""
        try:
            # 禁用同步按钮，防止重复操作
            self.btn_sync.config(state="disabled", text="同步中...")
            self.root.update()

            # 使用优化的数据库管理器获取文件夹列表
            with self.db_manager.get_connection() as conn:
                # 获取所有文件夹
                cursor = conn.execute("SELECT id, path FROM folders")
                folders = cursor.fetchall()

                # 获取所有单独的图片
                standalone_images = self.project_db.check_standalone_images()

                # 如果没有文件夹和单独图片，直接返回
                if not folders and not standalone_images:
                    self.btn_sync.config(state="normal", text="同步")
                    return

            # 使用project_db模块进行同步操作
            stats = self.project_db.sync_folders_in_db(folders, self.get_sort_key)

            # 处理单独的图片
            standalone_removed = 0
            for img_id, img_path in standalone_images:
                try:
                    # 检查图片是否存在
                    if not Path(img_path).exists():
                        # 图片已被删除，从数据库中移除
                        with self.db_manager.get_connection() as conn:
                            conn.execute("DELETE FROM images WHERE id = ?", (img_id,))
                            conn.commit()
                        standalone_removed += 1
                except Exception as e:
                    print(f"检查单独图片失败: {img_path}, 错误: {e}")

            stats["removed"] += standalone_removed

            # 显示同步结果
            print(f"同步完成: 添加{stats['added']}个, 删除{stats['removed']}个, 更新{stats['updated']}个")

            # 重新加载项目列表
            self.load_projects()

            # 🔧 修复：更新搜索范围选项，显示同步后的文件夹变化
            self.update_search_scope_options()

        except Exception as e:
            print(f"同步失败: {e}")
        finally:
            # 恢复同步按钮
            self.btn_sync.config(state="normal", text="同步")

    def quick_sync_check(self):
        """快速同步检查 - 仅检查是否有明显变化"""
        try:
            # 获取所有文件夹路径
            with self.db_manager.get_connection() as conn:
                cursor = conn.execute("SELECT COUNT(*) FROM folders")
                folder_count = cursor.fetchone()[0]

                cursor = conn.execute("SELECT COUNT(*) FROM images")
                image_count = cursor.fetchone()[0]

            # 如果数据库中没有任何数据，直接执行完整同步
            if folder_count == 0 and image_count == 0:
                self.sync_all_folders()
                return

            # 获取所有文件夹路径
            with self.db_manager.get_connection() as conn:
                cursor = conn.execute("SELECT path FROM folders")
                folder_paths = [row[0] for row in cursor.fetchall()]

            # 如果没有文件夹但有图片记录，可能是数据不一致，需要同步
            if not folder_paths and image_count > 0:
                self.sync_all_folders()
                return

            # 快速检查是否有文件夹不存在或明显的文件数量变化
            needs_sync = False
            for folder_path in folder_paths:
                folder_path_obj = Path(folder_path)
                if not folder_path_obj.exists():
                    needs_sync = True
                    break

                # 检查是否有子文件夹（递归扫描的情况）
                try:
                    extensions = (".jpg", ".jpeg", ".png", ".bmp", ".gi", ".ti")
                    # 使用递归扫描来匹配同步逻辑
                    current_files = sum(
                        1
                        for f in folder_path_obj.rglob("*")
                        if f.is_file() and f.suffix.lower() in extensions
                    )

                    # 获取数据库中的文件数量
                    with self.db_manager.get_connection() as conn:
                        cursor = conn.execute(
                            "SELECT COUNT(*) FROM images WHERE folder_id = (SELECT id FROM folders WHERE path = ?)",
                            (folder_path,),
                        )
                        db_count = cursor.fetchone()[0]

                    # 如果文件数量差异超过阈值，需要同步
                    if abs(current_files - db_count) > 0:
                        needs_sync = True
                        break

                except Exception:
                    # 如果检查出错，保守起见进行同步
                    needs_sync = True
                    break

            # 如果需要同步，自动执行
            if needs_sync:
                self.sync_all_folders()
            else:
                # 即使跳过同步，也要确保项目列表已加载
                self.load_projects()
                # 🔧 修复：确保搜索范围选项也是最新的
                self.update_search_scope_options()

        except Exception as e:
            print(f"快速同步检查失败: {e}")
            # 如果检查失败，执行完整同步
            self.sync_all_folders()

    def set_scroll_speed(self):
        """设置滚动速度"""
        try:
            # 获取选择的速度值
            speed_str = self.scroll_speed_var.get()
            speed = float(speed_str)

            # 更新滚动时间
            self.scroll_duration = speed

            # 保存设置到数据库
            self.save_scroll_speed_settings()

        except Exception as e:
            print(f"设置滚动速度失败: {e}")

    def manual_reorder_images(self, source_id, target_id, parent_folder):
        """手动重新排序图片"""
        try:
            # 记住当前选中的项目和展开状态
            current_selection = self.project_tree.selection()
            expanded_items = []
            for item in self.project_tree.get_children():
                if self.project_tree.item(item, "open"):
                    expanded_items.append(item)

            with sqlite3.connect(self.db_path) as conn:
                if parent_folder:
                    # 文件夹内的图片
                    folder_id = int(parent_folder.split("_")[1])

                    # 标记该文件夹为手动排序
                    conn.execute(
                        """
                        INSERT OR REPLACE INTO manual_sort_folders
                        (folder_id, is_manual_sort, last_manual_sort_time)
                        VALUES (?, 1, CURRENT_TIMESTAMP)
                    """,
                        (folder_id,),
                    )

                    # 获取文件夹内所有图片的当前顺序
                    cursor = conn.execute(
                        """
                        SELECT id, order_index
                        FROM images
                        WHERE folder_id = ?
                        ORDER BY order_index
                    """,
                        (folder_id,),
                    )
                    images = cursor.fetchall()

                    # 重新计算顺序
                    new_order = self._calculate_new_order(
                        images, int(source_id), int(target_id)
                    )

                    # 批量更新顺序
                    for img_id, new_index in new_order.items():
                        conn.execute(
                            """
                            UPDATE images
                            SET order_index = ?
                            WHERE id = ? AND folder_id = ?
                        """,
                            (new_index, img_id, folder_id),
                        )
                else:
                    # 独立图片
                    cursor = conn.execute(
                        """
                        SELECT id, order_index
                        FROM images
                        WHERE folder_id IS NULL
                        ORDER BY order_index
                    """
                    )
                    images = cursor.fetchall()

                    # 重新计算顺序
                    new_order = self._calculate_new_order(
                        images, int(source_id), int(target_id)
                    )

                    # 批量更新顺序
                    for img_id, new_index in new_order.items():
                        conn.execute(
                            """
                            UPDATE images
                            SET order_index = ?
                            WHERE id = ? AND folder_id IS NULL
                        """,
                            (new_index, img_id),
                        )

                conn.commit()

            # 重新加载项目列表
            self.load_projects()

            # 恢复展开状态
            for item in expanded_items:
                try:
                    self.project_tree.item(item, open=True)
                except Exception:
                    pass

            # 恢复选中状态，优先选中被移动的图片
            try:
                self.project_tree.selection_set(source_id)
                self.project_tree.focus(source_id)
                # 确保选中的项目可见
                self.project_tree.see(source_id)
            except Exception:
                # 如果无法选中源项目，尝试恢复之前的选中状态
                if current_selection:
                    try:
                        self.project_tree.selection_set(current_selection[0])
                        self.project_tree.focus(current_selection[0])
                    except Exception:
                        pass

        except Exception as e:
            print(f"手动排序失败: {e}")

    def _calculate_new_order(self, images, source_id, target_id):
        """计算新的排序顺序"""
        # 创建ID到索引的映射
        id_to_index = {img_id: idx for idx, (img_id, _) in enumerate(images)}

        # 获取源和目标的当前索引
        source_index = id_to_index[source_id]
        target_index = id_to_index[target_id]

        # 创建新的顺序列表
        image_ids = [img_id for img_id, _ in images]

        # 移除源项目
        image_ids.pop(source_index)

        # 插入到目标位置
        if source_index < target_index:
            # 向下拖拽，插入到目标位置
            image_ids.insert(target_index - 1, source_id)
        else:
            # 向上拖拽，插入到目标位置
            image_ids.insert(target_index, source_id)

        # 返回新的顺序映射
        return {img_id: idx for idx, img_id in enumerate(image_ids)}

    def reset_folder_sort(self):
        """重置文件夹排序为自动排序"""
        try:
            # 获取当前选中的项目
            selected_items = self.project_tree.selection()
            if not selected_items:
                return

            selected_item = selected_items[0]
            if not selected_item.startswith("folder_"):
                return

            folder_id = int(selected_item.split("_")[1])

            # 确认对话框
            from tkinter import messagebox

            result = messagebox.askyesno(
                "确认重置",
                "确定要重置此文件夹的排序吗？\n将按照文件名自动排序。",
                parent=self.root,
            )

            if not result:
                return

            with sqlite3.connect(self.db_path) as conn:
                # 移除手动排序标记
                conn.execute(
                    """
                    DELETE FROM manual_sort_folders
                    WHERE folder_id = ?
                """,
                    (folder_id,),
                )

                # 获取文件夹内所有图片
                cursor = conn.execute(
                    """
                    SELECT id, path
                    FROM images
                    WHERE folder_id = ?
                """,
                    (folder_id,),
                )
                images = cursor.fetchall()

                # 按文件名重新排序
                sorted_images = sorted(images, key=lambda x: self.get_sort_key(x[1]))

                # 更新排序索引
                for index, (img_id, _) in enumerate(sorted_images):
                    conn.execute(
                        """
                        UPDATE images
                        SET order_index = ?
                        WHERE id = ?
                    """,
                        (index, img_id),
                    )

                conn.commit()

            # 重新加载项目列表
            self.load_projects()

            messagebox.showinfo("完成", "文件夹排序已重置", parent=self.root)

        except Exception as e:
            print(f"重置排序失败: {e}")
            messagebox.showerror("错误", f"重置排序失败: {e}", parent=self.root)

    def __del__(self):
        """析构函数，清理资源"""
        try:
            if hasattr(self, "global_hotkeys_enabled") and self.global_hotkeys_enabled:
                self.cleanup_global_hotkeys()
        except Exception:
            pass

    def manage_original_mark(
        self, item_type, item_id, action="check", mark_type="loop"
    ):
        """统一管理原图标记的添加、移除和检查 - 调用yuantu.py中的方法"""
        return self.yuantu_manager.manage_original_mark(
            item_type, item_id, action, mark_type
        )

    def add_original_mark(self, item_type, item_id, mark_type="loop"):
        """添加原图标记 - 调用yuantu.py中的方法"""
        return self.yuantu_manager.add_original_mark(item_type, item_id, mark_type)

    def remove_original_mark(self, item_type, item_id):
        """移除原图标记 - 调用yuantu.py中的方法"""
        return self.yuantu_manager.remove_original_mark(item_type, item_id)

    def check_original_mark(self, item_type, item_id):
        """检查是否有原图标记 - 调用yuantu.py中的方法"""
        return self.yuantu_manager.check_original_mark(item_type, item_id)

    def get_original_mark_type(self, item_type, item_id):
        """获取原图标记类型 - 调用yuantu.py中的方法"""
        return self.yuantu_manager.get_original_mark_type(item_type, item_id)

    def should_use_original_mode(self, image_id):
        """判断图片是否应该使用原图模式 - 调用yuantu.py中的方法"""
        return self.yuantu_manager.should_use_original_mode(image_id)

    def add_custom_yellow_color(self):
        """打开颜色选择器窗口"""
        self.config_manager.open_color_picker_window()

    def apply_color_from_picker_no_close(self, r, g, b):
        """从颜色选择器应用颜色（不关闭窗口版本）"""
        # 调用config_manager的方法，不关闭窗口
        self.config_manager.apply_color_from_picker(r, g, b, window=None)

    def _schedule_ui_update(self, update_type):
        """调度UI更新"""
        if not self._ui_update_scheduled:
            self._ui_update_scheduled = True
            self.root.after_idle(self._perform_ui_update, update_type)

    def _perform_ui_update(self, update_type):
        """执行UI更新"""
        self._ui_update_scheduled = False
        if update_type == 'preview_lines':
            # 更新预览线条
            if hasattr(self, 'update_preview_lines'):
                self.update_preview_lines()

if __name__ == "__main__":
    app = None
    try:
        print(f"工作目录: {Path.cwd()}")
        
        # 检查关键文件
        if getattr(sys, 'frozen', False):
            exe_dir = Path(sys.executable).parent
            print(f"可执行文件目录: {exe_dir}")
            
        root = tk.Tk()
        
        # 创建应用实例（窗口最大化在ImageProjector.__init__中处理）
        app = ImageProjector(root)

        def on_closing():
            """程序关闭时的清理函数"""
            try:
                if (
                    app
                    and hasattr(app, "global_hotkeys_enabled")
                    and app.global_hotkeys_enabled
                ):
                    app.cleanup_global_hotkeys()
                    # print("程序关闭时清理全局热键")

                # 关闭数据库管理器
                if app and hasattr(app, "db_manager"):
                    app.db_manager.close()
                    # print("数据库连接已关闭")

                # print("程序正在关闭...")
                root.quit()
                root.destroy()
            except Exception as e:
                print(f"关闭程序时出错: {e}")

        root.bind("<Configure>", lambda e: app.update_image())
        root.protocol("WM_DELETE_WINDOW", on_closing)
        
        root.mainloop()
    except Exception as e:
        print(f"程序错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 确保清理全局热键
        try:
            if (
                app
                and hasattr(app, "global_hotkeys_enabled")
                and app.global_hotkeys_enabled
            ):
                app.cleanup_global_hotkeys()
        except Exception:
            pass
