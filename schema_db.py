"""
数据库结构管理模块
负责数据库表的创建、索引管理和结构维护
使用直接的sqlite3.connect方式保持高效性能
"""

import sqlite3
from pathlib import Path


class DatabaseSchema:
    """数据库结构管理器"""
    
    def __init__(self, db_path: str):
        """
        初始化数据库结构管理器
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
    
    def init_database(self):
        """初始化数据库表结构和索引"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # 创建所有表
                self._create_tables(conn)
                
                # 创建所有索引
                self._create_indexes(conn)
                
                conn.commit()
                print("数据库初始化完成")
                
        except Exception as e:
            print(f"数据库初始化失败: {e}")
            raise e
    
    def _create_tables(self, conn: sqlite3.Connection):
        """创建所有数据库表"""
        
        # UI设置表
        conn.execute("""
            CREATE TABLE IF NOT EXISTS ui_settings (
                key TEXT PRIMARY KEY,
                value TEXT NOT NULL
            )
        """)
        
        # 通用设置表
        conn.execute("""
            CREATE TABLE IF NOT EXISTS settings (
                key TEXT PRIMARY KEY,
                value TEXT NOT NULL
            )
        """)
        
        # 文件夹表
        conn.execute("""
            CREATE TABLE IF NOT EXISTS folders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                path TEXT UNIQUE NOT NULL,
                order_index INTEGER,
                created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 图片表
        conn.execute("""
            CREATE TABLE IF NOT EXISTS images (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                path TEXT UNIQUE NOT NULL,
                folder_id INTEGER,
                last_modified TIMESTAMP,
                order_index INTEGER,
                FOREIGN KEY (folder_id) REFERENCES folders(id),
                UNIQUE(path)
            )
        """)

        # 关键帧表
        conn.execute("""
            CREATE TABLE IF NOT EXISTS keyframes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                image_id INTEGER NOT NULL,
                position REAL NOT NULL,
                y_position INTEGER NOT NULL,
                order_index INTEGER,
                FOREIGN KEY (image_id) REFERENCES images(id)
            )
        """)
        
        # 关键帧时间记录表
        conn.execute("""
            CREATE TABLE IF NOT EXISTS keyframe_timings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                image_id INTEGER NOT NULL,
                keyframe_id INTEGER NOT NULL,
                duration REAL NOT NULL,
                sequence_order INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (image_id) REFERENCES images(id),
                FOREIGN KEY (keyframe_id) REFERENCES keyframes(id)
            )
        """)
        
        # 原图模式时间记录表
        conn.execute("""
            CREATE TABLE IF NOT EXISTS original_mode_timings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                base_image_id INTEGER NOT NULL,
                from_image_id INTEGER NOT NULL,
                to_image_id INTEGER NOT NULL,
                duration REAL NOT NULL,
                sequence_order INTEGER NOT NULL,
                mark_type TEXT DEFAULT 'loop',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (base_image_id) REFERENCES images(id),
                FOREIGN KEY (from_image_id) REFERENCES images(id),
                FOREIGN KEY (to_image_id) REFERENCES images(id)
            )
        """)
        
        # 原图标记表
        conn.execute("""
            CREATE TABLE IF NOT EXISTS original_marks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                item_type TEXT NOT NULL CHECK (item_type IN ('folder', 'image')),
                item_id INTEGER NOT NULL,
                mark_type TEXT NOT NULL DEFAULT 'loop' CHECK (mark_type IN ('loop', 'sequence')),
                created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(item_type, item_id)
            )
        """)

        # 检查并添加mark_type列（如果不存在）
        try:
            conn.execute(
                'ALTER TABLE original_marks ADD COLUMN mark_type TEXT DEFAULT "loop" CHECK (mark_type IN ("loop", "sequence"))'
            )
        except sqlite3.OperationalError:
            # 列已存在，忽略错误
            pass

        # 手动排序文件夹表
        conn.execute("""
            CREATE TABLE IF NOT EXISTS manual_sort_folders (
                folder_id INTEGER PRIMARY KEY,
                is_manual_sort BOOLEAN DEFAULT 0,
                last_manual_sort_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (folder_id) REFERENCES folders(id) ON DELETE CASCADE
            )
        """)

        # 图片显示位置表（支持同一图片在多个位置显示）
        conn.execute("""
            CREATE TABLE IF NOT EXISTS image_display_locations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                image_id INTEGER NOT NULL,
                location_type TEXT NOT NULL CHECK (location_type IN ('folder', 'root')),
                folder_id INTEGER,
                order_index INTEGER,
                created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (image_id) REFERENCES images(id) ON DELETE CASCADE,
                FOREIGN KEY (folder_id) REFERENCES folders(id) ON DELETE CASCADE,
                UNIQUE(image_id, location_type, folder_id)
            )
        """)
    
    def _create_indexes(self, conn: sqlite3.Connection):
        """创建所有数据库索引"""

        # 图片索引
        conn.execute("CREATE INDEX IF NOT EXISTS idx_folder_id ON images(folder_id)")
        conn.execute("CREATE INDEX IF NOT EXISTS idx_order_images ON images(order_index)")
        conn.execute("CREATE INDEX IF NOT EXISTS idx_images_name ON images(name)")
        conn.execute("CREATE INDEX IF NOT EXISTS idx_images_folder_order ON images(folder_id, order_index)")

        # 文件夹索引
        conn.execute("CREATE INDEX IF NOT EXISTS idx_order_folders ON folders(order_index)")

        # 关键帧索引
        conn.execute("CREATE INDEX IF NOT EXISTS idx_keyframes_image ON keyframes(image_id)")
        conn.execute("CREATE INDEX IF NOT EXISTS idx_keyframes_order ON keyframes(order_index)")

        # 关键帧时间记录索引（如果表存在）
        conn.execute("CREATE INDEX IF NOT EXISTS idx_timing_image ON keyframe_timings(image_id)")
        conn.execute("CREATE INDEX IF NOT EXISTS idx_timing_sequence ON keyframe_timings(image_id, sequence_order)")

        # 原图模式时间记录索引（如果表存在）
        conn.execute("CREATE INDEX IF NOT EXISTS idx_original_base ON original_mode_timings(base_image_id)")
        conn.execute("CREATE INDEX IF NOT EXISTS idx_original_sequence ON original_mode_timings(base_image_id, sequence_order)")

        # 原图标记索引
        conn.execute("CREATE INDEX IF NOT EXISTS idx_original_marks ON original_marks(item_type, item_id)")

        # 手动排序文件夹索引
        conn.execute("CREATE INDEX IF NOT EXISTS idx_manual_sort ON manual_sort_folders(folder_id)")
    
    def get_table_info(self, table_name: str) -> list:
        """获取表结构信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(f"PRAGMA table_info({table_name})")
                return cursor.fetchall()
        except Exception as e:
            print(f"获取表信息失败: {e}")
            return []
    
    def get_all_tables(self) -> list:
        """获取所有表名"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    "SELECT name FROM sqlite_master WHERE type='table' ORDER BY name"
                )
                return [row[0] for row in cursor.fetchall()]
        except Exception as e:
            print(f"获取表列表失败: {e}")
            return []
    
    def get_all_indexes(self) -> list:
        """获取所有索引名"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    "SELECT name FROM sqlite_master WHERE type='index' ORDER BY name"
                )
                return [row[0] for row in cursor.fetchall()]
        except Exception as e:
            print(f"获取索引列表失败: {e}")
            return []
    
    def check_database_integrity(self) -> bool:
        """检查数据库完整性"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("PRAGMA integrity_check")
                result = cursor.fetchone()
                return result[0] == "ok"
        except Exception as e:
            print(f"数据库完整性检查失败: {e}")
            return False
