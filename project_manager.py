"""
项目文件管理模块
负责项目的加载、搜索、选择、导入等文件管理功能
"""

import sqlite3
import re
from pathlib import Path
from tkinter import messagebox, filedialog
import time


class ProjectManager:
    """项目文件管理器"""
    
    def __init__(self, app_instance):
        """初始化项目文件管理器

        Args:
            app_instance: 主应用实例的引用
        """
        self.app = app_instance


    def load_projects(self):
        """加载项目列表（优化版本：使用单一连接）"""
        # 清空当前树
        for item in self.app.project_tree.get_children():
            self.app.project_tree.delete(item)

        try:
            # 使用单一连接执行所有查询
            with self.app.db_manager.get_connection() as conn:
                # 批量获取所有原图标记
                cursor = conn.execute("SELECT item_type, item_id FROM original_marks")
                marks = cursor.fetchall()
                mark_set = (
                    {(item_type, item_id) for item_type, item_id in marks}
                    if marks
                    else set()
                )

                # 获取所有文件夹
                cursor = conn.execute(
                    "SELECT id, name, path FROM folders ORDER BY order_index"
                )
                folders = cursor.fetchall()

                # 获取所有图片
                cursor = conn.execute(
                    """
                    SELECT id, name, path, folder_id, order_index
                    FROM images
                    ORDER BY folder_id, order_index
                """
                )
                images = cursor.fetchall()

                # 获取手动排序的文件夹列表
                manual_sort_cursor = conn.execute(
                    """
                    SELECT folder_id FROM manual_sort_folders
                    WHERE is_manual_sort = 1
                """
                )
                manual_sort_folders = {row[0] for row in manual_sort_cursor.fetchall()}

                # 添加文件夹到树中
                folder_nodes = {}
                if folders:
                    for folder_id, folder_name, folder_path in folders:
                        has_mark = ("folder", folder_id) in mark_set
                        is_manual_sort = folder_id in manual_sort_folders

                        # 根据标记和排序类型确定图标
                        if has_mark:
                            if is_manual_sort:
                                mark_icon = "★ 🔢 "  # 有标记且手动排序
                            else:
                                mark_icon = "★ "  # 有标记但自动排序
                        else:
                            if is_manual_sort:
                                mark_icon = "☆ 🔢 "  # 无标记但手动排序
                            else:
                                mark_icon = "☆ "  # 无标记且自动排序

                        display_name = mark_icon + folder_name

                        folder_node = self.app.project_tree.insert(
                            "",
                            "end",
                            iid=f"folder_{folder_id}",
                            text="",
                            values=(display_name,),
                        )
                        folder_nodes[folder_id] = folder_node

                # 处理图片
                if images:
                    # 按文件夹分组处理图片
                    current_folder_id = None
                    folder_images = []

                    for (
                        image_id,
                        image_name,
                        image_path,
                        folder_id,
                        order_index,
                    ) in images:
                        if folder_id != current_folder_id:
                            if folder_images:
                                self._process_folder_images(
                                    folder_images, folder_nodes, mark_set
                                )
                            current_folder_id = folder_id
                            folder_images = []
                        folder_images.append(
                            (image_id, image_name, image_path, folder_id, order_index)
                        )

                    if folder_images:
                        self._process_folder_images(
                            folder_images, folder_nodes, mark_set
                        )

        except Exception as e:
            print(f"加载项目列表失败: {e}")
            # 回退到原始方法
            self._load_projects_fallback()
    
    def _load_projects_fallback(self):
        """回退的项目加载方法（使用原始的多次连接方式）"""
        try:
            # 批量获取所有原图标记
            marks = self.app.safe_db_execute(
                "SELECT item_type, item_id FROM original_marks", fetch="all"
            )
            mark_set = (
                {(item_type, item_id) for item_type, item_id in marks}
                if marks
                else set()
            )

            # 获取所有文件夹
            folders = self.app.safe_db_execute(
                "SELECT id, name, path FROM folders ORDER BY order_index", fetch="all"
            )
            if not folders:
                return

            # 获取手动排序的文件夹列表
            manual_sort_folders_result = self.app.safe_db_execute(
                "SELECT folder_id FROM manual_sort_folders WHERE is_manual_sort = 1",
                fetch="all",
            )
            manual_sort_folders = (
                {row[0] for row in manual_sort_folders_result}
                if manual_sort_folders_result
                else set()
            )

            # 添加文件夹到树中
            folder_nodes = {}
            for folder_id, folder_name, folder_path in folders:
                has_mark = ("folder", folder_id) in mark_set
                is_manual_sort = folder_id in manual_sort_folders

                # 根据标记和排序类型确定图标
                if has_mark:
                    if is_manual_sort:
                        mark_icon = "★ 🔢 "  # 有标记且手动排序
                    else:
                        mark_icon = "★ "  # 有标记但自动排序
                else:
                    if is_manual_sort:
                        mark_icon = "☆ 🔢 "  # 无标记但手动排序
                    else:
                        mark_icon = "☆ "  # 无标记且自动排序

                display_name = mark_icon + folder_name

                folder_node = self.app.project_tree.insert(
                    "",
                    "end",
                    iid=f"folder_{folder_id}",
                    text="",
                    values=(display_name,),
                )
                folder_nodes[folder_id] = folder_node

            # 获取所有图片
            images = self.app.safe_db_execute(
                "SELECT id, name, path, folder_id, order_index FROM images ORDER BY folder_id, order_index",
                fetch="all",
            )
            if images:
                # 按文件夹分组处理图片
                current_folder_id = None
                folder_images = []

                for image_id, image_name, image_path, folder_id, order_index in images:
                    if folder_id != current_folder_id:
                        if folder_images:
                            self._process_folder_images(
                                folder_images, folder_nodes, mark_set
                            )
                        current_folder_id = folder_id
                        folder_images = []
                    folder_images.append(
                        (image_id, image_name, image_path, folder_id, order_index)
                    )

                if folder_images:
                    self._process_folder_images(folder_images, folder_nodes, mark_set)

        except Exception as e:
            print(f"回退方法也失败: {e}")

        self.app.update_search_scope_options()
        self.app.update_project_tree_marks()
    
    def _process_folder_images(self, folder_images, folder_nodes, mark_set):
        """处理文件夹内的图片"""
        # 按文件夹分组处理，只有手动排序的文件夹才显示序号
        current_folder_id = None
        folder_image_index = 0
        manual_sort_folders = set()

        # 预先获取所有手动排序的文件夹ID
        try:
            with sqlite3.connect(self.app.db_path) as conn:
                cursor = conn.execute(
                    """
                    SELECT folder_id FROM manual_sort_folders
                    WHERE is_manual_sort = 1
                """
                )
                manual_sort_folders = {row[0] for row in cursor.fetchall()}
        except Exception as e:
            print(f"获取手动排序文件夹失败: {e}")

        for image_id, image_name, image_path, folder_id, order_index in folder_images:
            # 如果切换到新文件夹，重置序号计数
            if folder_id != current_folder_id:
                current_folder_id = folder_id
                folder_image_index = 1
            else:
                folder_image_index += 1

            # 如果图片属于文件夹，添加到对应文件夹节点下
            if folder_id is not None and folder_id in folder_nodes:
                parent = folder_nodes[folder_id]
                # 只有手动排序的文件夹才显示序号
                if folder_id in manual_sort_folders:
                    display_name = f"{folder_image_index:02d}. {image_name}"
                else:
                    display_name = image_name
            else:
                parent = ""
                # 独立图片不显示序号，只显示标记
                has_mark = ("image", image_id) in mark_set
                if has_mark:
                    display_name = f"● {image_name}"
                else:
                    display_name = image_name

            self.app.project_tree.insert(
                parent, "end", iid=str(image_id), text="", values=(display_name,)
            )

    def search_projects(self, event=None):
        """搜索项目"""
        search_term = self.app.search_var.get().lower()
        scope_selection = self.app.search_scope_var.get()  # 获取选中的搜索范围

        # 清空当前树
        for item in self.app.project_tree.get_children():
            self.app.project_tree.delete(item)

        # 如果搜索词为空，根据范围重新加载
        if not search_term:
            self.load_projects()  # 重新加载所有项目
            return

        # 解析搜索范围，获取文件夹ID
        search_folder_id = None
        if scope_selection != "全部":
            match = re.search(r"\(ID:(\d+)\)$", scope_selection)
            if match:
                try:
                    search_folder_id = int(match.group(1))
                except ValueError:
                    print(f"无法解析文件夹ID: {scope_selection}")
                    scope_selection = "全部"  # 解析失败则退回搜索全部

        # 搜索数据库 - 只搜索图片内容，不搜索文件夹
        try:
            with sqlite3.connect(self.app.db_path) as conn:

                # 2. 搜索图片 (根据搜索范围调整查询条件)
                if search_folder_id is None:
                    # 范围是 "全部"，搜索所有图片
                    cursor = conn.execute(
                        """
                        SELECT i.id, i.name, i.path, i.order_index, i.folder_id, f.name as folder_name
                        FROM images i
                        LEFT JOIN folders f ON i.folder_id = f.id
                        WHERE LOWER(i.name) LIKE ?
                        ORDER BY i.order_index
                    """,
                        (f"%{search_term}%",),
                    )
                else:
                    # 范围是特定文件夹，仅搜索该文件夹下的图片
                    cursor = conn.execute(
                        """
                        SELECT i.id, i.name, i.path, i.order_index, i.folder_id, f.name as folder_name
                        FROM images i
                        LEFT JOIN folders f ON i.folder_id = f.id
                        WHERE LOWER(i.name) LIKE ? AND i.folder_id = ?
                        ORDER BY i.order_index
                    """,
                        (f"%{search_term}%", search_folder_id),
                    )

                # 获取手动排序的文件夹列表
                manual_sort_cursor = conn.execute(
                    """
                    SELECT folder_id FROM manual_sort_folders
                    WHERE is_manual_sort = 1
                """
                )
                manual_sort_folders = {
                    row[0] for row in manual_sort_cursor.fetchall()
                }

                # 获取原图标记
                marks_cursor = conn.execute("SELECT item_type, item_id FROM original_marks")
                mark_set = {(item_type, item_id) for item_type, item_id in marks_cursor.fetchall()}

                # 添加匹配的图片
                for img_id, img_name, img_path, order_index, folder_id, folder_name in cursor.fetchall():
                    # 构建显示名称
                    if folder_id is not None:
                        # 图片属于文件夹，简单显示格式：图片名-文件夹名
                        final_display_name = f"{img_name}-{folder_name}"
                    else:
                        # 独立图片
                        has_mark = ("image", img_id) in mark_set
                        if has_mark:
                            final_display_name = f"● {img_name}"
                        else:
                            final_display_name = img_name

                    # 使用 project_tree.insert 添加图片，简单显示
                    self.app.project_tree.insert(
                        "",
                        "end",
                        iid=str(img_id),  # 图片ID直接用数字字符串
                        text="",  # 空的图标列
                        values=(final_display_name,),  # 显示名称
                    )

        except Exception as e:
            print(f"搜索失败: {e}")
            messagebox.showerror("错误", f"搜索失败: {e}")

    def on_project_select(self, event):
        """处理项目选择事件"""
        selected_item = self.app.project_tree.selection()
        if not selected_item:
            return

        item_id = selected_item[0]

        # 如果选中的是文件夹，检查是否有原图标记
        if item_id.startswith("folder_"):
            try:
                folder_id = int(item_id.split("_")[1])
                with sqlite3.connect(self.app.db_path) as conn:
                    # 检查文件夹是否有原图标记
                    cursor = conn.execute(
                        """
                        SELECT 1 FROM original_marks
                        WHERE item_type = 'folder' AND item_id = ?
                        """,
                        (folder_id,),
                    )
                    folder_has_original_mark = cursor.fetchone() is not None

                    # 根据文件夹标记状态切换原图模式
                    if hasattr(self.app, '_last_should_use_original'):
                        if folder_has_original_mark:
                            # 文件夹有标记，开启原图模式
                            if not self.app.original_mode:
                                # print(f"文件夹有原图标记，自动开启原图模式")
                                self.app.toggle_original_mode()
                        else:
                            # 文件夹无标记
                            if self.app._last_should_use_original and self.app.original_mode:
                                # 从有标记切换到无标记，自动关闭原图模式
                                # print(f"文件夹无原图标记，自动关闭原图模式")
                                self.app.toggle_original_mode()

                        # 更新状态
                        self.app._last_should_use_original = folder_has_original_mark
                    else:
                        # 首次选择文件夹，初始化状态
                        self.app._last_should_use_original = folder_has_original_mark
                        if folder_has_original_mark and not self.app.original_mode:
                            # print(f"文件夹有原图标记，自动开启原图模式")
                            self.app.toggle_original_mode()

            except Exception as e:
                print(f"处理文件夹原图标记失败: {e}")
            return

        try:
            # 从数据库获取图片信息
            with sqlite3.connect(self.app.db_path) as conn:
                cursor = conn.execute(
                    """
                    SELECT path, name, order_index FROM images WHERE id = ?
                """,
                    (int(item_id),),
                )
                result = cursor.fetchone()

                if result:
                    image_path, image_name, order_index = result

                    # 加载图片
                    self.app.load_image(image_path)

                    # 确保current_image_id已设置
                    self.app.current_image_id = int(item_id)

                    # 检查当前图片是否有原图标记
                    cursor = conn.execute(
                        """
                        SELECT 1 FROM original_marks
                        WHERE item_type = 'image' AND item_id = ?
                    """,
                        (int(item_id),),
                    )
                    image_has_mark = cursor.fetchone() is not None

                    # 检查图片所在文件夹是否有原图标记
                    cursor = conn.execute(
                        """
                        SELECT folder_id FROM images WHERE id = ?
                        """,
                        (int(item_id),),
                    )
                    folder_result = cursor.fetchone()
                    folder_has_mark = False

                    if folder_result and folder_result[0]:
                        cursor = conn.execute(
                            """
                            SELECT 1 FROM original_marks
                            WHERE item_type = 'folder' AND item_id = ?
                            """,
                            (folder_result[0],),
                        )
                        folder_has_mark = cursor.fetchone() is not None

                    # 图片本身有标记或所在文件夹有标记，都应该开启原图模式
                    current_should_use_original = image_has_mark or folder_has_mark

                    # 智能切换原图模式
                    if hasattr(self.app, '_last_should_use_original'):
                        # 根据标记状态智能切换原图模式
                        if current_should_use_original:
                            # 当前图片有标记，开启原图模式
                            if not self.app.original_mode:
                                self.app.toggle_original_mode()
                        else:
                            # 当前图片无标记
                            if self.app._last_should_use_original and self.app.original_mode:
                                # 从有标记切换到无标记，自动关闭原图模式
                                self.app.toggle_original_mode()

                        # 更新上一张图片的标记状态
                        self.app._last_should_use_original = current_should_use_original
                    else:
                        # 首次加载，初始化状态
                        self.app._last_should_use_original = current_should_use_original
                        if current_should_use_original and not self.app.original_mode:
                            self.app.toggle_original_mode()

        except Exception as e:
            print(f"加载图片失败: {e}")

    def add_image_to_project(self, file_path, update_list=True):
        """添加图片到项目列表（用于拖拽功能）"""
        try:
            # 获取文件名作为项目名称
            name = Path(file_path).stem
            with sqlite3.connect(self.app.db_path) as conn:
                # 检查图片是否已存在于根目录（folder_id IS NULL）
                cursor = conn.execute(
                    "SELECT id FROM images WHERE path = ? AND folder_id IS NULL",
                    (file_path,),
                )
                existing = cursor.fetchone()
                if existing:
                    # 如果图片已作为独立图片存在于根目录，不需要重复添加
                    print(f"图片 {name} 已在根目录中")
                    return

                # 检查图片是否存在于某个文件夹中
                cursor = conn.execute(
                    "SELECT id, folder_id FROM images WHERE path = ? AND folder_id IS NOT NULL",
                    (file_path,),
                )
                existing_in_folder = cursor.fetchone()
                if existing_in_folder:
                    # 如果图片已存在于某个文件夹中，询问用户是否要复制到根目录
                    existing_id, existing_folder_id = existing_in_folder

                    # 获取文件夹名称
                    cursor = conn.execute(
                        "SELECT name FROM folders WHERE id = ?", (existing_folder_id,)
                    )
                    folder_result = cursor.fetchone()
                    folder_name = folder_result[0] if folder_result else "未知文件夹"

                    response = messagebox.askyesno(
                        "图片已存在",
                        f"图片 {name} 已存在于文件夹 '{folder_name}' 中。\n是否要在根目录中创建副本？"
                    )
                    if not response:
                        return

                # 如果图片完全不存在，则添加新记录
                # 获取当前最大的order_index
                cursor = conn.execute(
                    "SELECT COALESCE(MAX(order_index), -1) FROM images WHERE folder_id IS NULL"
                )
                max_order = cursor.fetchone()[0]

                # 添加图片（不关联文件夹）
                cursor = conn.execute(
                    """
                    INSERT INTO images (name, path, folder_id, order_index)
                    VALUES (?, ?, NULL, ?)
                """,
                    (name, file_path, max_order + 1),
                )

                conn.commit()

                # 根据参数决定是否立即更新项目列表
                if update_list:
                    self.load_projects()

        except Exception as e:
            print(f"添加图片到项目失败: {e}")

    def import_single_image(self):
        """导入单个图片"""
        file_path = filedialog.askopenfilename(
            filetypes=[("Image files", "*.jpg *.jpeg *.png *.bmp *.gif *.tif")]
        )
        if file_path:
            try:
                # 获取文件名作为项目名称
                name = Path(file_path).stem
                with sqlite3.connect(self.app.db_path) as conn:
                    # 检查图片是否已存在
                    cursor = conn.execute(
                        "SELECT id FROM images WHERE path = ?", (file_path,)
                    )
                    existing = cursor.fetchone()
                    if existing:
                        # 如果图片已存在，直接加载该图片
                        image_id = existing[0]
                        messagebox.showinfo("提示", "该图片已存在，将直接打开")
                        self.app.load_image(file_path)

                        # 在项目树中选中该图片
                        self.app.project_tree.selection_set(str(image_id))
                        self.app.project_tree.see(str(image_id))
                        return

                    # 获取当前最大的order_index
                    cursor = conn.execute(
                        "SELECT COALESCE(MAX(order_index), -1) FROM images WHERE folder_id IS NULL"
                    )
                    max_order = cursor.fetchone()[0]

                    # 添加图片（不关联文件夹）
                    cursor = conn.execute(
                        """
                        INSERT INTO images (name, path, folder_id, order_index)
                        VALUES (?, ?, NULL, ?)
                    """,
                        (name, file_path, max_order + 1),
                    )

                    # 获取新插入图片的ID
                    image_id = cursor.lastrowid

                    conn.commit()

                    # 更新项目列表
                    self.load_projects()

                    # 加载新导入的图片
                    self.app.load_image(file_path)

                    # 在项目树中选中新导入的图片
                    self.app.project_tree.selection_set(str(image_id))
                    self.app.project_tree.see(str(image_id))

            except Exception as e:
                print(f"导入失败: {e}")
                messagebox.showerror("错误", f"导入失败: {e}")

    def save_project_config(self):
        """保存项目配置到数据库"""
        try:
            with sqlite3.connect(self.app.db_path) as conn:
                # 更新排序索引
                for i, item in enumerate(self.app.project_tree.get_children()):
                    item_id = int(item)
                    conn.execute(
                        """
                        UPDATE images
                        SET order_index = ?
                        WHERE id = ?
                    """,
                        (i, item_id),
                    )
                conn.commit()
        except Exception as e:
            print(f"保存项目配置失败: {e}")

    def _select_image_in_tree(self, image_id):
        """安全地在项目树中选中图片"""
        try:
            # 检查项目是否存在于树中
            if self.app.project_tree.exists(str(image_id)):
                self.app.project_tree.selection_set(str(image_id))
                self.app.project_tree.see(str(image_id))
            else:
                print(f"项目树中未找到图片ID: {image_id}")
        except Exception as e:
            print(f"选中图片失败: {e}")
