"""
播放控制模块
负责时间录制、自动播放、播放次数设置、倒计时显示等播放相关功能
"""

import tkinter as tk
from tkinter import messagebox


class PlaybackController:
    """播放控制器"""
    
    def __init__(self, app_instance):
        """初始化播放控制器
        
        Args:
            app_instance: 主应用实例的引用
        """
        self.app = app_instance
    
    def toggle_timing_recording(self):
        """切换时间录制状态"""
        if not self.app.time_recorder:
            messagebox.showerror("错误", "时间录制模块未初始化")
            return

        if not hasattr(self.app, "current_image_id"):
            messagebox.showinfo("提示", "请先选择一张图片")
            return

        # 检查是否为原图模式
        if self.app.original_mode and hasattr(self.app, "yuantu_manager"):
            # 原图模式录制
            self._toggle_original_mode_recording()
        else:
            # 普通关键帧录制
            self._toggle_normal_recording()

    def _toggle_normal_recording(self):
        """普通关键帧录制"""
        if not self.app.is_recording_timing:
            # 开始录制
            if self.app.time_recorder.start_recording(self.app.current_image_id):
                self.app.is_recording_timing = True
                self.app.btn_record.config(text="停止", bg="#ff6666")
                self.app.update_button_status()
            else:
                messagebox.showerror("错误", "开始录制失败")
        else:
            # 停止录制前，记录当前帧的停留时间
            if self.app.current_keyframe_index >= 0:
                keyframes = self.app.get_keyframes(self.app.current_image_id)
                if keyframes and self.app.current_keyframe_index < len(keyframes):
                    current_keyframe_id = keyframes[self.app.current_keyframe_index][0]
                    self.app.time_recorder.record_keyframe_timing(current_keyframe_id)

            # 停止录制
            if self.app.time_recorder.stop_recording():
                self.app.is_recording_timing = False
                self.app.btn_record.config(text="录制", bg="#ffcccc")
                self.app.update_button_status()

                # 录制结束后自动启用播放功能
                self.app.root.after(200, self._auto_start_play_after_recording)
            else:
                messagebox.showerror("错误", "停止录制失败")

    def _toggle_original_mode_recording(self):
        """原图模式录制"""
        if not self.app.time_recorder.is_original_mode_recording:
            # 开始原图模式录制
            similar_images = self.app.yuantu_manager.similar_images
            if not similar_images:
                messagebox.showinfo(
                    "提示", "当前图片没有相似图片，无法进行原图模式录制"
                )
                return

            # 获取标记类型
            mark_type = self.app.yuantu_manager.get_original_mark_type("image", self.app.current_image_id)
            if not mark_type:
                mark_type = "loop"  # 默认循环标记

            if self.app.time_recorder.start_original_mode_recording(
                self.app.current_image_id, similar_images, mark_type
            ):
                self.app.is_recording_timing = True
                self.app.btn_record.config(text="停止", bg="#ff6666")
                self.app.update_button_status()
                print(
                    f"开始原图模式录制，相似图片数量: {len(similar_images)}，标记类型: {mark_type}"
                )
                print("请使用方向键或鼠标在相似图片之间切换来录制切换时间")
            else:
                messagebox.showerror("错误", "开始原图模式录制失败")
        else:
            # 停止原图模式录制
            if self.app.time_recorder.stop_original_mode_recording():
                self.app.is_recording_timing = False
                self.app.btn_record.config(text="录制", bg="#ffcccc")
                print("原图模式录制完成")

                # 延迟更新按钮状态，确保数据库操作完成
                self.app.root.after(100, self.app.update_button_status)

                # 录制结束后自动启用播放功能
                self.app.root.after(200, self._auto_start_play_after_recording)
            else:
                messagebox.showerror("错误", "停止原图模式录制失败")

    def _auto_start_play_after_recording(self):
        """录制结束后自动启动播放功能"""
        try:
            # 检查是否有当前图片
            if not hasattr(self.app, 'current_image_id') or not self.app.current_image_id:
                return

            # 检查是否已经在播放状态
            if self.app.is_auto_playing:
                return

            # 检查是否有录制的时间数据
            if not self.app.time_recorder:
                return

            # 根据模式检查时间数据
            has_timing_data = False
            if self.app.original_mode and hasattr(self.app, "yuantu_manager"):
                # 原图模式：检查原图模式时间数据
                has_timing_data = self.app.time_recorder.has_original_mode_timing_data(
                    self.app.current_image_id
                )
            else:
                # 普通模式：检查关键帧时间数据
                has_timing_data = self.app.time_recorder.has_timing_data(self.app.current_image_id)

            # 如果有时间数据，自动启动播放
            if has_timing_data:
                self.toggle_auto_play()

        except Exception as e:
            print(f"自动启动播放失败: {e}")

    def toggle_auto_play(self):
        """切换自动播放状态"""
        if not self.app.auto_player:
            messagebox.showerror("错误", "自动播放模块未初始化")
            return

        if not hasattr(self.app, "current_image_id"):
            messagebox.showinfo("提示", "请先选择一张图片")
            return

        # 检查是否为原图模式
        if self.app.original_mode and hasattr(self.app, "yuantu_manager"):
            # 原图模式播放
            self._toggle_original_mode_auto_play()
        else:
            # 普通关键帧播放
            self._toggle_normal_auto_play()

    def _toggle_normal_auto_play(self):
        """普通关键帧自动播放"""
        if not self.app.is_auto_playing:
            # 确保播放次数设置同步到auto_player
            if self.app.auto_player:
                loop_enabled = self.app.target_play_count == -1
                self.app.auto_player.set_loop_mode(loop_enabled)
                if hasattr(self.app.auto_player, "target_play_count"):
                    self.app.auto_player.target_play_count = self.app.target_play_count
                    # print(f"开始播放前同步播放次数: {self.app.target_play_count}")

            # 开始自动播放
            if self.app.auto_player.start_auto_play(self.app.current_image_id):
                self.app.is_auto_playing = True
                self.app.btn_play.config(text="停止", bg="#90EE90")
                self.app.update_button_status()
                # 显示倒计时组件
                self.show_countdown_display()
            else:
                messagebox.showerror("错误", "该图片没有录制的时间序列数据")
        else:
            # 停止自动播放
            if self.app.auto_player.stop_auto_play():
                self.app.is_auto_playing = False
                self.app.btn_play.config(text="播放", bg="#f0f0f0")  # 重置背景颜色
                self.app.update_button_status()
                # 隐藏倒计时组件
                self.hide_countdown_display()

    def _toggle_original_mode_auto_play(self):
        """原图模式自动播放"""
        if not self.app.auto_player.is_original_mode_playing:
            # 确保播放次数设置同步到auto_player
            if self.app.auto_player:
                loop_enabled = self.app.target_play_count == -1
                self.app.auto_player.set_loop_mode(loop_enabled)
                if hasattr(self.app.auto_player, "target_play_count"):
                    self.app.auto_player.target_play_count = self.app.target_play_count
                    # print(f"原图模式开始播放前同步播放次数: {self.app.target_play_count}")

            # 开始原图模式自动播放
            similar_images = self.app.yuantu_manager.similar_images
            if not similar_images:
                messagebox.showinfo(
                    "提示", "当前图片没有相似图片，无法进行原图模式播放"
                )
                return

            if self.app.auto_player.start_original_mode_auto_play(
                self.app.current_image_id, similar_images
            ):
                self.app.is_auto_playing = True
                self.app.btn_play.config(text="停止", bg="#90EE90")
                self.app.update_button_status()
                # 显示倒计时组件
                self.show_countdown_display()
                # print(f"开始原图模式自动播放，相似图片数量: {len(similar_images)}")
            else:
                messagebox.showerror("错误", "该图片没有录制的原图模式时间序列数据")
        else:
            # 停止原图模式播放
            if self.app.auto_player.stop_original_mode_auto_play():
                self.app.is_auto_playing = False
                self.app.btn_play.config(text="播放", bg="#f0f0f0")
                self.app.update_button_status()
                # 隐藏倒计时组件
                self.hide_countdown_display()

    def show_countdown_display(self):
        """显示倒计时组件（现在固定显示，此方法保留兼容性）"""
        # 倒计时组件现在固定显示，不需要动态显示/隐藏
        pass

    def hide_countdown_display(self):
        """隐藏倒计时组件（现在固定显示，此方法保留兼容性）"""
        # 倒计时组件现在固定显示，不需要动态显示/隐藏
        pass

    def toggle_countdown_pause(self):
        """切换倒计时暂停/继续状态"""
        if not self.app.auto_player or not self.app.is_auto_playing:
            return

        if hasattr(self.app.auto_player, 'is_paused') and self.app.auto_player.is_paused:
            # 当前是暂停状态，恢复播放
            if self.app.original_mode and hasattr(self.app.auto_player, 'resume_original_mode_auto_play'):
                # 原图模式恢复
                if self.app.auto_player.resume_original_mode_auto_play():
                    self.app.btn_pause_resume.config(text="暂停", bg="#f0f0f0")  # 恢复普通颜色
                    print("恢复原图模式倒计时播放")
            elif hasattr(self.app.auto_player, 'resume_auto_play'):
                # 普通模式恢复
                if self.app.auto_player.resume_auto_play():
                    self.app.btn_pause_resume.config(text="暂停", bg="#f0f0f0")  # 恢复普通颜色
                    print("恢复倒计时播放")
        else:
            # 当前是播放状态，暂停播放
            if self.app.original_mode and hasattr(self.app.auto_player, 'pause_original_mode_auto_play'):
                # 原图模式暂停
                if self.app.auto_player.pause_original_mode_auto_play():
                    self.app.btn_pause_resume.config(text="继续", bg="#98FB98")  # 变绿色
                    print("暂停原图模式倒计时播放")
            elif hasattr(self.app.auto_player, 'pause_auto_play'):
                # 普通模式暂停
                if self.app.auto_player.pause_auto_play():
                    self.app.btn_pause_resume.config(text="继续", bg="#98FB98")  # 变绿色
                    print("暂停倒计时播放")

    def set_play_count(self):
        """设置播放次数（自定义输入）"""
        # 创建输入对话框
        dialog = tk.Toplevel(self.app.root)
        dialog.title("设置播放次数")
        dialog.geometry("300x150")
        dialog.resizable(False, False)
        dialog.transient(self.app.root)
        dialog.grab_set()

        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (300 // 2)
        y = (dialog.winfo_screenheight() // 2) - (150 // 2)
        dialog.geometry(f"300x150+{x}+{y}")

        # 说明文字
        tk.Label(dialog, text="请输入播放次数：", font=("Arial", 10)).pack(pady=10)

        # 输入框
        entry_frame = tk.Frame(dialog)
        entry_frame.pack(pady=5)

        tk.Label(entry_frame, text="次数：").pack(side=tk.LEFT)
        entry = tk.Entry(entry_frame, width=10)
        entry.pack(side=tk.LEFT, padx=5)
        entry.insert(
            0, str(self.app.target_play_count) if self.app.target_play_count != -1 else "∞"
        )

        # 提示文字
        tk.Label(dialog, text="输入数字或 ∞ 表示无限循环", font=("Arial", 8), fg="gray").pack()

        # 按钮框架
        button_frame = tk.Frame(dialog)
        button_frame.pack(pady=10)

        def validate_and_save():
            """验证输入并保存设置"""
            value = entry.get().strip()

            if value == "∞" or value.lower() == "in":
                new_count = -1
            else:
                try:
                    new_count = int(value)
                    if new_count <= 0:
                        messagebox.showerror("错误", "播放次数必须大于0")
                        return
                except ValueError:
                    messagebox.showerror("错误", "请输入有效的数字或 ∞")
                    return

            # 保存设置
            self.app.target_play_count = new_count
            self.app.save_play_count_setting()

            # 更新按钮显示
            self._update_play_count_button()

            # 同步到auto_player
            if self.app.auto_player:
                loop_enabled = self.app.target_play_count == -1
                self.app.auto_player.set_loop_mode(loop_enabled)
                if hasattr(self.app.auto_player, "target_play_count"):
                    self.app.auto_player.target_play_count = self.app.target_play_count

            print(
                f"播放次数设定为: {'无限循环' if self.app.target_play_count == -1 else f'{self.app.target_play_count}次'}"
            )
            dialog.destroy()

        def cancel():
            """取消设置"""
            dialog.destroy()

        # 按钮
        tk.Button(button_frame, text="确定", command=validate_and_save, width=8).pack(
            side=tk.LEFT, padx=5
        )
        tk.Button(button_frame, text="取消", command=cancel, width=8).pack(
            side=tk.LEFT, padx=5
        )

        # 绑定回车键
        entry.bind("<Return>", lambda e: validate_and_save())
        entry.bind("<Escape>", lambda e: cancel())

    def _update_play_count_button(self):
        """更新播放次数按钮显示"""
        if self.app.target_play_count == -1:
            text = "∞次"
        else:
            text = f"{self.app.target_play_count}次"

        if hasattr(self.app, "btn_loop"):
            self.app.btn_loop.config(text=text)

    def clear_timing_data(self):
        """清除当前图片的时间录制数据"""
        if not hasattr(self.app, "current_image_id"):
            messagebox.showinfo("提示", "请先选择一张图片")
            return

        if not self.app.time_recorder:
            messagebox.showerror("错误", "时间录制模块未初始化")
            return

        # 检查是否为原图模式
        if self.app.original_mode and hasattr(self.app, "yuantu_manager"):
            # 原图模式清除
            self._clear_original_mode_timing_data()
        else:
            # 普通关键帧清除
            self._clear_normal_timing_data()

    def _clear_normal_timing_data(self):
        """清除普通关键帧时间数据"""
        result = messagebox.askyesno(
            "确认清除", "确定要清除当前图片的时间录制数据吗？\n此操作不可撤销。"
        )

        if result:
            # 先停止正在进行的录制或播放
            if self.app.is_recording_timing:
                # 清除前先记录当前帧的停留时间
                if self.app.current_keyframe_index >= 0:
                    keyframes = self.app.get_keyframes(self.app.current_image_id)
                    if keyframes and self.app.current_keyframe_index < len(keyframes):
                        current_keyframe_id = keyframes[self.app.current_keyframe_index][0]
                        self.app.time_recorder.record_keyframe_timing(current_keyframe_id)

                self.app.time_recorder.stop_recording()
                self.app.is_recording_timing = False
                self.app.btn_record.config(text="录制", bg="#e8f5e8")

            if self.app.is_auto_playing:
                self.app.auto_player.stop_auto_play()
                self.app.is_auto_playing = False
                self.app.btn_play.config(text="播放", bg="#e8f0f8")

            # 清除时间数据
            if self.app.time_recorder.clear_timing_data(self.app.current_image_id):
                messagebox.showinfo("成功", "时间录制数据已清除")
                self.app.update_button_status()
            else:
                messagebox.showerror("错误", "清除时间录制数据失败")

    def _clear_original_mode_timing_data(self):
        """清除原图模式时间数据"""
        result = messagebox.askyesno(
            "确认清除", "确定要清除当前图片的原图模式时间录制数据吗？\n此操作不可撤销。"
        )

        if result:
            # 先停止正在进行的录制或播放
            if self.app.is_recording_timing:
                self.app.time_recorder.stop_original_mode_recording()
                self.app.is_recording_timing = False
                self.app.btn_record.config(text="录制", bg="#e8f5e8")

            if self.app.is_auto_playing:
                self.app.auto_player.stop_original_mode_auto_play()
                self.app.is_auto_playing = False
                self.app.btn_play.config(text="播放", bg="#e8f0f8")

            # 清除原图模式时间数据
            if self.app.time_recorder.clear_original_mode_timing_data(self.app.current_image_id):
                messagebox.showinfo("成功", "原图模式时间录制数据已清除")
                self.app.update_button_status()
            else:
                messagebox.showerror("错误", "清除原图模式时间录制数据失败")

    def on_auto_play_finished(self):
        """自动播放结束时的回调函数"""
        # 更新播放状态
        self.app.is_auto_playing = False

        # 更新按钮状态
        self.app.btn_play.config(text="播放", bg="#f0f0f0")  # 重置背景颜色
        self.app.update_button_status()

        # print("自动播放结束，已切换回播放按钮状态")
