"""
上下文菜单管理模块
负责处理项目树的右键菜单、删除操作、原图标记等功能
"""

import sqlite3
import tkinter as tk
from tkinter import messagebox


class ContextMenuManager:
    """上下文菜单管理器"""
    
    def __init__(self, app_instance):
        """初始化上下文菜单管理器
        
        Args:
            app_instance: 主应用实例的引用
        """
        self.app = app_instance
    
    def show_context_menu(self, event):
        """显示右键菜单"""
        # 获取点击位置的项目
        item = self.app.project_tree.identify_row(event.y)

        # 清除之前的所有菜单项
        self.app.context_menu.delete(0, "end")

        # 如果没有选中任何项目（点击空白区域），显示刷新菜单
        if not item:
            self.app.context_menu.add_command(
                label="刷新",
                command=self.app.sync_all_folders,
                font=(self.app.default_font, self.app.menu_font_size),
            )
            # 显示菜单
            self.app.context_menu.post(event.x_root, event.y_root)
            return

        # 选中该项目
        self.app.project_tree.selection_set(item)

        # 根据项目类型动态构建菜单
        is_folder = item.startswith("folder_")
        try:
            if is_folder:
                self._build_folder_context_menu(item)
            else:
                self._build_image_context_menu(item)

        except Exception as e:
            print(f"更新右键菜单失败: {e}")

        # 显示菜单
        self.app.context_menu.post(event.x_root, event.y_root)

    def _build_folder_context_menu(self, item):
        """构建文件夹的右键菜单"""
        # 文件夹：显示删除和原图标记选项
        self.app.context_menu.add_command(
            label="删除",
            command=self.delete_selected_item,
            font=(self.app.default_font, self.app.menu_font_size),
        )
        self.app.context_menu.add_separator()

        folder_id = int(item.split("_")[1])
        has_mark = self.app.check_original_mark("folder", folder_id)

        # 添加相应的菜单项
        if has_mark:
            # 如果已有标记，显示取消标记选项
            mark_type = self.app.get_original_mark_type("folder", folder_id)
            current_mark_text = (
                "循环原图标记" if mark_type == "loop" else "顺序原图标记"
            )
            self.app.context_menu.add_command(
                label=f"取消{current_mark_text}",
                command=self.unmark_original,
                font=(self.app.default_font, self.app.menu_font_size),
            )
        else:
            # 如果没有标记，显示两种标记选项
            self.app.context_menu.add_command(
                label="循环原图标记",
                command=lambda: self.mark_as_original("loop"),
                font=(self.app.default_font, self.app.menu_font_size),
            )
            self.app.context_menu.add_command(
                label="顺序原图标记",
                command=lambda: self.mark_as_original("sequence"),
                font=(self.app.default_font, self.app.menu_font_size),
            )

        # 添加重置排序选项
        self.app.context_menu.add_separator()
        self.app.context_menu.add_command(
            label="重置排序",
            command=self.app.reset_folder_sort,
            font=(self.app.default_font, self.app.menu_font_size),
        )

    def _build_image_context_menu(self, item):
        """构建图片的右键菜单"""
        # 检查是否在文件夹内
        parent_item = self.app.project_tree.parent(item)
        if not parent_item:
            # 独立图片：显示删除和标记选项
            self.app.context_menu.add_command(
                label="删除",
                command=self.delete_selected_item,
                font=(self.app.default_font, self.app.menu_font_size),
            )
            self.app.context_menu.add_separator()

            image_id = int(item)
            has_mark = self.app.check_original_mark("image", image_id)

            # 添加相应的菜单项
            if has_mark:
                # 如果已有标记，显示取消标记选项
                mark_type = self.app.get_original_mark_type("image", image_id)
                current_mark_text = (
                    "循环原图标记" if mark_type == "loop" else "顺序原图标记"
                )
                self.app.context_menu.add_command(
                    label=f"取消{current_mark_text}",
                    command=self.unmark_original,
                    font=(self.app.default_font, self.app.menu_font_size),
                )
            else:
                # 如果没有标记，显示两种标记选项
                self.app.context_menu.add_command(
                    label="循环原图标记",
                    command=lambda: self.mark_as_original("loop"),
                    font=(self.app.default_font, self.app.menu_font_size),
                )
                self.app.context_menu.add_command(
                    label="顺序原图标记",
                    command=lambda: self.mark_as_original("sequence"),
                    font=(self.app.default_font, self.app.menu_font_size),
                )
        else:
            # 文件夹内的图片：不显示任何选项，直接返回
            return

    def delete_selected_item(self):
        """删除选中的项目"""
        selected = self.app.project_tree.selection()
        if not selected:
            return

        item_id = selected[0]

        # 判断是文件夹还是图片
        is_folder = item_id.startswith("folder_")

        # 对于图片，检查是否在文件夹内（额外安全检查）
        if not is_folder:
            parent_item = self.app.project_tree.parent(item_id)
            if parent_item:
                messagebox.showinfo(
                    "提示", "文件夹内的图片不能单独删除，请删除整个文件夹"
                )
                return

        # 确认删除
        if not self._confirm_deletion(item_id, is_folder):
            return

        try:
            self._perform_deletion(item_id, is_folder)
            
            # 从树中删除项目
            self.app.project_tree.delete(item_id)

            # 如果当前显示的是被删除的图片，清除显示
            if (
                hasattr(self.app, "current_image_id")
                and str(self.app.current_image_id) == item_id
            ):
                self.app.clear_current_image()

        except Exception as e:
            print(f"删除项目失败: {e}")
            messagebox.showerror("错误", f"删除失败: {e}")

    def _confirm_deletion(self, item_id, is_folder):
        """确认删除操作"""
        if is_folder:
            display_name = self.app.project_tree.item(item_id, "values")[0]
            # 移除图标前缀获取纯名称
            if display_name.startswith("[●] ") or display_name.startswith("[ ] "):
                folder_name = display_name[4:]
            else:
                folder_name = display_name
            return messagebox.askyesno(
                "确认删除",
                f"确定要删除文件夹 '{folder_name}' 及其所有图片吗？\n此操作不会删除实际文件。",
            )
        else:
            display_name = self.app.project_tree.item(item_id, "values")[0]
            # 检查是否在文件夹内（文件夹内的图片没有图标前缀）
            parent_item = self.app.project_tree.parent(item_id)
            if parent_item:  # 在文件夹内，直接使用显示名称
                image_name = display_name
            else:  # 独立图片，移除图标前缀（如果有的话）
                if display_name.startswith("● "):
                    image_name = display_name[2:]
                else:
                    image_name = display_name
            return messagebox.askyesno(
                "确认删除",
                f"确定要删除图片 '{image_name}' 吗？\n此操作不会删除实际文件。",
            )

    def _perform_deletion(self, item_id, is_folder):
        """执行删除操作"""
        with sqlite3.connect(self.app.db_path) as conn:
            if is_folder:
                # 从文件夹ID字符串中提取数字部分
                folder_id = int(item_id.split("_")[1])

                # 删除文件夹下的所有图片
                conn.execute("DELETE FROM images WHERE folder_id = ?", (folder_id,))

                # 删除文件夹的原图标记
                conn.execute(
                    "DELETE FROM original_marks WHERE item_type = ? AND item_id = ?",
                    ("folder", folder_id),
                )

                # 删除文件夹
                conn.execute("DELETE FROM folders WHERE id = ?", (folder_id,))
            else:
                # 删除图片
                image_id = int(item_id)

                # 删除图片的所有关键帧
                conn.execute(
                    "DELETE FROM keyframes WHERE image_id = ?", (image_id,)
                )

                # 删除图片的原图标记
                conn.execute(
                    "DELETE FROM original_marks WHERE item_type = ? AND item_id = ?",
                    ("image", image_id),
                )

                # 删除图片
                conn.execute("DELETE FROM images WHERE id = ?", (image_id,))

            conn.commit()

    def mark_as_original(self, mark_type="loop"):
        """标记选中项目为原图 - 调用yuantu.py中的方法"""
        self.app.yuantu_manager.mark_as_original(mark_type)

    def unmark_original(self):
        """取消选中项目的原图标记 - 调用yuantu.py中的方法"""
        self.app.yuantu_manager.unmark_original()

    def update_project_tree_marks(self):
        """更新项目树中的原图标记显示 - 调用yuantu.py中的方法"""
        self.app.yuantu_manager.update_project_tree_marks()

    def _update_item_mark_display(self, item_id):
        """更新单个项目的标记显示 - 调用yuantu.py中的方法"""
        self.app.yuantu_manager._update_item_mark_display(item_id)
