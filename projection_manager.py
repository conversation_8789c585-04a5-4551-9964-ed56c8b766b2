"""
投影显示管理器模块 - GPU加速版本

负责管理歌谱投影控制器的投影显示功能，包括：
- 投影窗口管理 (支持GPU加速同步)
- 多屏幕支持
- 屏幕信息获取
- 投影同步逻辑 (GPU优化)
- 全局热键管理
- GPU渲染同步

作者: AI Assistant
创建时间: 2024
版本: V1.9.9 (GPU Enhanced)
"""

import tkinter as tk
from tkinter import messagebox
import screeninfo
import time
from PIL import Image, ImageTk

from pynput import keyboard as pynput_keyboard

# GPU加速支持
try:
    from gpu_scroll_engine import TkinterGPUBridge
    GPU_AVAILABLE = True
except ImportError:
    GPU_AVAILABLE = False

# GPU投影渲染器支持
try:
    from gpu_projection_renderer import create_gpu_projection_renderer
    GPU_PROJECTION_AVAILABLE = True
except ImportError:
    GPU_PROJECTION_AVAILABLE = False


class ProjectionManager:
    """投影显示管理器
    
    负责管理投影窗口、多屏幕支持、同步逻辑和全局热键等功能
    """
    
    def __init__(self, main_app):
        """初始化投影管理器
        
        Args:
            main_app: 主应用程序实例，必须包含以下属性和方法：
                - root: tkinter根窗口
                - canvas: 主画布
                - image: 当前图片对象
                - screen_combo: 屏幕选择下拉框
                - btn_show: 投影按钮
                - original_mode: 是否为原图模式
                - zoom_ratio: 缩放比例
                - is_inverted: 是否黄字模式
                - is_simple_inverted: 是否简单反色模式
                - apply_yellow_text_effect(): 应用黄字效果方法
                - simple_invert_image(): 简单反色方法
        """
        self.main_app = main_app
        
        # 投影窗口相关属性
        self.second_window = None
        self.second_canvas = None
        self.second_photo = None
        self.second_image_on_canvas = None
        
        # 屏幕管理属性
        self.screens = []
        self.current_screen_index = 0
        
        # 状态管理属性
        self.sync_enabled = False
        self.global_hotkeys_enabled = False

        # pynput 全局热键管理
        self.global_hotkeys_listener = None
        
        # 性能优化相关
        self._size_cache = {}
        self._last_sync_time = 0
        self._sync_throttle_interval = 0.016  # 约60FPS
        
        # 错误计数器（用于诊断）
        self._sync_error_count = 0
        self._max_error_count = 10
        
        # GPU渲染支持
        self.gpu_bridge = None
        self.use_gpu_sync = False
        
        # GPU投影直接渲染器
        self.gpu_projection_renderer = None
        self.use_gpu_projection = False
        
        # 尝试初始化投影GPU支持
        if GPU_AVAILABLE:
            try:
                # 投影屏GPU桥接器（稍后初始化）
                self.projection_gpu_bridge = None
                pass
            except Exception as e:
                pass
                
        # 初始化GPU投影渲染器支持
        if GPU_PROJECTION_AVAILABLE:
            pass
        
        # 初始化屏幕信息
        self._init_screen_info()
        
        # print("投影管理器初始化完成")
    
    def _init_screen_info(self):
        """初始化屏幕信息"""
        try:
            self.screens = screeninfo.get_monitors()
            if not self.screens:
                # 回退到主屏幕
                self.screens = [
                    screeninfo.Monitor(
                        x=0,
                        y=0,
                        width=self.main_app.root.winfo_screenwidth(),
                        height=self.main_app.root.winfo_screenheight(),
                    )
                ]
            pass
                
        except Exception as e:
            pass
            # 使用默认屏幕信息
            self.screens = [
                screeninfo.Monitor(
                    x=0,
                    y=0,
                    width=self.main_app.root.winfo_screenwidth(),
                    height=self.main_app.root.winfo_screenheight(),
                )
            ]
    
    # ==================== 公有API方法 ====================
    
    def toggle_projection(self):
        """切换投影显示状态
        
        Returns:
            bool: 操作是否成功
        """
        try:
            if self.second_window:
                return self._close_projection()
            else:
                return self._open_projection()
        except Exception as e:
            pass
            return False
    
    def get_monitor_info(self):
        """获取显示器信息
        
        Returns:
            list: 显示器信息列表，每个元素包含name, x, y, width, height, is_primary
        """
        monitors = []
        try:
            for i, monitor in enumerate(self.screens):
                if hasattr(monitor, 'is_primary') and monitor.is_primary:
                    name = "主显示器"
                else:
                    name = f"显示器{i+1}"
                monitors.append({
                    "name": name,
                    "x": monitor.x,
                    "y": monitor.y,
                    "width": monitor.width,
                    "height": monitor.height,
                    "is_primary": getattr(monitor, 'is_primary', False),
                })
        except Exception as e:
            pass
        
        return monitors
    
    def get_effective_screen_size(self, is_projection_screen=False):
        """统一计算有效显示区域
        
        Args:
            is_projection_screen (bool): 是否为投影屏幕
            
        Returns:
            tuple: (effective_width, effective_height)
        """
        try:
            if is_projection_screen:
                # 投影屏幕使用完整屏幕尺寸
                if hasattr(self.main_app, 'screen_combo') and self.main_app.screen_combo:
                    selected = self.main_app.screen_combo.current()
                    if 0 <= selected < len(self.screens):
                        screen = self.screens[selected]
                        return screen.width, screen.height
                # 回退到第一个屏幕
                if self.screens:
                    return self.screens[0].width, self.screens[0].height
                # 最后回退到主屏幕尺寸
                return (
                    self.main_app.root.winfo_screenwidth(),
                    self.main_app.root.winfo_screenheight()
                )
            else:
                # 主屏幕使用画布尺寸
                canvas_width = self.main_app.canvas.winfo_width()
                canvas_height = self.main_app.canvas.winfo_height()
                
                # 确保返回有效的尺寸
                if canvas_width <= 1 or canvas_height <= 1:
                    # 画布尺寸无效时的回退逻辑
                    return 800, 600  # 默认尺寸
                
                return canvas_width, canvas_height
                
        except Exception as e:
            pass
            # 返回安全的默认值
            return 1920, 1080
    
    def setup_global_hotkeys(self):
        """设置全局热键

        Returns:
            bool: 设置是否成功
        """
        # 避免重复设置
        if self.global_hotkeys_enabled:
            pass
            return True

        try:
            # 先清理可能存在的旧热键
            self._cleanup_old_hotkeys()

            # 定义热键处理函数
            def on_pageup_press():
                self.main_app.root.after(
                    0, lambda: (
                        self.main_app.canvas.focus_set(),
                        self._handle_pageup_key()
                    )
                )

            def on_pagedown_press():
                self.main_app.root.after(
                    0, lambda: (
                        self.main_app.canvas.focus_set(),
                        self._handle_pagedown_key()
                    )
                )

            def on_escape_press():
                self.main_app.root.after(0, lambda: self._handle_escape_key())

            # 使用 pynput 的 GlobalHotKeys 注册全局热键
            hotkey_mapping = {
                '<page_up>': on_pageup_press,
                '<page_down>': on_pagedown_press,
                '<esc>': on_escape_press
            }

            self.global_hotkeys_listener = pynput_keyboard.GlobalHotKeys(hotkey_mapping)
            self.global_hotkeys_listener.start()

            self.global_hotkeys_enabled = True
            # print("全局热键注册成功: PageUp, PageDown, Escape")
            return True

        except Exception as e:
            pass
            return False

    def cleanup_global_hotkeys(self):
        """清理全局热键

        Returns:
            bool: 清理是否成功
        """
        try:
            # 停止 pynput 的全局热键监听器
            if self.global_hotkeys_listener is not None:
                self.global_hotkeys_listener.stop()
                self.global_hotkeys_listener = None

            self.global_hotkeys_enabled = False
            # print("全局热键已清理")
            return True

        except Exception as e:
            pass
            return False
    
    # ==================== 私有辅助方法 ====================
    
    def _cleanup_old_hotkeys(self):
        """清理可能存在的旧热键"""
        try:
            # 对于 pynput，如果已有监听器则先停止
            if hasattr(self, 'global_hotkeys_listener') and self.global_hotkeys_listener is not None:
                self.global_hotkeys_listener.stop()
                self.global_hotkeys_listener = None
        except Exception:
            pass  # 忽略清理错误
    
    def _close_projection(self):
        """关闭投影窗口
        
        Returns:
            bool: 关闭是否成功
        """
        try:
            if self.second_window:
                self.second_window.destroy()
                self.second_window = None
                self.second_canvas = None
                self.second_photo = None
                self.second_image_on_canvas = None
                
                # 更新主应用UI状态
                if hasattr(self.main_app, 'btn_show'):
                    self.main_app.btn_show.configure(bg="#f0f0f0", text="投影")
                
                self.sync_enabled = False
                
                # GPU投影渲染器已禁用
                # if self.use_gpu_projection:
                #     try:
                #         self.enable_gpu_projection(False)
                #         print("✅ GPU投影直接渲染已停止")
                #     except Exception as e:
                #         print(f"⚠️  停止GPU投影失败: {e}")
                
                # 清理全局热键
                self.cleanup_global_hotkeys()
                # print("投影已关闭，全局热键已禁用")
                
            return True
            
        except Exception as e:
            pass
            return False
    
    def _open_projection(self):
        """打开投影窗口
        
        Returns:
            bool: 打开是否成功
        """
        try:
            if not self.main_app.image:
                messagebox.showwarning("警告", "请先打开一张图片！")
                return False

            if len(self.screens) > 1:
                success = self.show_on_second_screen()
                if success:
                    if hasattr(self.main_app, 'btn_show'):
                        self.main_app.btn_show.configure(bg="#90EE90", text="结束")
                    
                    # 启用全局热键
                    if not self.global_hotkeys_enabled:
                        self.setup_global_hotkeys()
                    # print("投影已开启")
                    return True
            else:
                messagebox.showwarning("警告", "未检测到第二个显示器！")
                return False
                
        except Exception as e:
            pass
            messagebox.showerror("错误", f"投影开启失败: {str(e)}")
            return False
    
    def _handle_pageup_key(self):
        """处理PageUp按键 - 委托给主应用处理"""
        try:
            if hasattr(self.main_app, 'handle_pageup_key'):
                self.main_app.handle_pageup_key()
        except Exception as e:
            pass

    def _handle_pagedown_key(self):
        """处理PageDown按键 - 委托给主应用处理"""
        try:
            if hasattr(self.main_app, 'handle_pagedown_key'):
                self.main_app.handle_pagedown_key()
        except Exception as e:
            pass

    def _handle_escape_key(self):
        """处理ESC按键 - 结束投影"""
        try:
            if self.second_window:
                self._close_projection()
                pass
        except Exception as e:
            pass
    
    def _validate_screen(self, screen):
        """验证屏幕是否可用
        
        Args:
            screen: 屏幕对象
            
        Returns:
            bool: 屏幕是否可用
        """
        try:
            # 检查屏幕属性
            if not hasattr(screen, 'width') or not hasattr(screen, 'height'):
                return False
            
            # 检查屏幕尺寸是否合理
            if screen.width <= 0 or screen.height <= 0:
                return False
                
            # 检查屏幕是否是主显示器（不允许投影到主显示器）
            if hasattr(screen, "is_primary") and screen.is_primary:
                return False
                
            return True
            
        except Exception:
            return False
    
    # ==================== 待实现的方法占位 ====================
    
    def show_on_second_screen(self):
        """显示到选定的屏幕
        
        Returns:
            bool: 显示是否成功
        """
        try:
            if not self.main_app.image:
                messagebox.showwarning("警告", "请先打开一张图片！")
                return False

            if len(self.screens) < 1:
                messagebox.showwarning("警告", "未检测到可用的显示器！")
                return False

            # 获取选定的屏幕
            selected = self.main_app.screen_combo.current()
            if selected < 0 or selected >= len(self.screens):
                messagebox.showerror("错误", "选定的显示器无效！")
                return False
                
            screen = self.screens[selected]
            self.current_screen_index = selected

            # 检查是否是主显示器
            if hasattr(screen, "is_primary") and screen.is_primary:
                messagebox.showwarning("警告", "不能投影到主显示器！")
                return False

            # 如果已有投影窗口，先销毁
            if self.second_window:
                self.second_window.destroy()

            # 创建新窗口
            self.second_window = tk.Toplevel(self.main_app.root)
            self.second_window.title("投影")
            self.second_window.overrideredirect(True)

            # 创建画布
            self.second_canvas = tk.Canvas(
                self.second_window, bg="black", highlightthickness=0
            )
            self.second_canvas.pack(fill=tk.BOTH, expand=True)

            # 使用统一的有效显示区域计算
            screen_width, screen_height = self.get_effective_screen_size(
                is_projection_screen=True
            )

            # 计算图片尺寸
            new_width, new_height = self._calculate_image_size(screen_width, screen_height)

            # 创建并显示图片
            success = self._display_image_on_projection_window(
                new_width, new_height, screen_width, screen_height
            )
            
            if not success:
                return False

            # 设置窗口位置和大小到选定的屏幕
            self.second_window.geometry(
                f"{screen.width}x{screen.height}+{screen.x}+{screen.y}"
            )

            # 设置副屏更新
            self.second_window.after(40, self.second_window.update)

            # 绑定事件
            self.second_window.bind("<Escape>", lambda e: self._close_projection())

            # 首次打开时开启同步
            self.sync_enabled = True
            
            # GPU投影渲染器暂时禁用，使用传统投影方式
            # if GPU_PROJECTION_AVAILABLE:
            #     try:
            #         if self.enable_gpu_projection(True):
            #             print("🚀 GPU投影直接渲染已启动")
            #             # 如果GPU投影成功启动，加载当前图片
            #             if hasattr(self.main_app, 'current_path') and self.main_app.current_path:
            #                 self.start_gpu_projection(self.main_app.current_path)
            #     except Exception as e:
            #         print(f"⚠️  GPU投影启动失败，使用传统投影: {e}")
            
            # print(f"投影窗口创建成功，屏幕: {screen.width}x{screen.height}")
            return True

        except Exception as e:
            print(f"显示到第二屏幕失败: {e}")
            messagebox.showerror("错误", f"投影显示失败: {str(e)}")
            return False
    
    def _calculate_image_size(self, screen_width, screen_height):
        """计算投影图片的尺寸
        
        Args:
            screen_width: 屏幕宽度
            screen_height: 屏幕高度
            
        Returns:
            tuple: (new_width, new_height)
        """
        try:
            if self.main_app.original_mode:
                # 原图模式：根据显示模式选择缩放策略
                width_ratio = screen_width / self.main_app.image.width
                height_ratio = screen_height / self.main_app.image.height

                if self.main_app.original_display_mode == 'stretch':
                    # 拉伸模式：高度按比例缩放确保内容完整，宽度拉伸填满屏幕
                    scale_ratio = height_ratio
                else:
                    # 适中模式：选择较小的缩放比例以确保完整显示
                    scale_ratio = min(width_ratio, height_ratio)

                # 智能缩放策略：根据图片和屏幕的关系调整
                if scale_ratio < 1:
                    # 图片大于屏幕：缩小到适合屏幕
                    pass  # 使用计算出的scale_ratio
                else:
                    # 图片小于屏幕：智能放大以更好地利用屏幕空间
                    screen_area = screen_width * screen_height
                    image_area = self.main_app.image.width * self.main_app.image.height
                    area_ratio = screen_area / image_area

                    # 动态计算最大放大倍数
                    if area_ratio > 16:  # 屏幕面积是图片的16倍以上
                        max_scale = 6.0  # 允许更大的放大倍数
                    elif area_ratio > 9:  # 屏幕面积是图片的9倍以上
                        max_scale = 4.0
                    elif area_ratio > 4:  # 屏幕面积是图片的4倍以上
                        max_scale = 3.0
                    else:
                        max_scale = 2.0  # 保守的放大倍数

                    # 应用最大放大限制
                    scale_ratio = min(scale_ratio, max_scale)

                if self.main_app.original_display_mode == 'stretch':
                    # 拉伸模式：宽度填满屏幕，高度按比例缩放
                    new_width = screen_width
                    new_height = int(self.main_app.image.height * scale_ratio)
                else:
                    # 适中模式：等比缩放
                    new_width = int(self.main_app.image.width * scale_ratio)
                    new_height = int(self.main_app.image.height * scale_ratio)
            else:
                # 正常模式下的缩放逻辑
                base_ratio = screen_width / self.main_app.image.width
                final_ratio = base_ratio * self.main_app.zoom_ratio
                new_width = int(self.main_app.image.width * final_ratio)
                new_height = int(self.main_app.image.height * final_ratio)

            return new_width, new_height
            
        except Exception as e:
            print(f"计算图片尺寸失败: {e}")
            return 800, 600  # 返回默认尺寸
    
    def _display_image_on_projection_window(self, new_width, new_height, screen_width, screen_height):
        """在投影窗口上显示图片
        
        Args:
            new_width: 图片新宽度
            new_height: 图片新高度
            screen_width: 屏幕宽度
            screen_height: 屏幕高度
            
        Returns:
            bool: 显示是否成功
        """
        try:
            # 统一的实时处理逻辑
            resized_image = self.main_app.image.resize(
                (new_width, new_height), Image.Resampling.LANCZOS
            )

            # 根据模式应用效果
            if self.main_app.is_inverted:
                resized_image = self.main_app.apply_yellow_text_effect(resized_image)
            elif self.main_app.is_simple_inverted:
                resized_image = self.main_app.simple_invert_image(resized_image)
                
            self.second_photo = ImageTk.PhotoImage(resized_image)

            # 计算居中位置 - 水平和垂直都居中
            x = max(0, (screen_width - new_width) // 2)
            y = max(0, (screen_height - new_height) // 2)

            # 显示新图片 - 在原图模式下居中显示
            if self.main_app.original_mode:
                self.second_image_on_canvas = self.second_canvas.create_image(
                    x, y, anchor=tk.NW, image=self.second_photo
                )
            else:
                # 正常模式保持原有逻辑（顶部显示）
                self.second_image_on_canvas = self.second_canvas.create_image(
                    x, 0, anchor=tk.NW, image=self.second_photo
                )

            # 设置投影窗口的滚动区域
            if self.main_app.original_mode:
                # 在原图模式下，如果图片完全适合屏幕，则不需要滚动
                if new_height <= screen_height:
                    self.second_canvas.configure(
                        scrollregion=(0, 0, screen_width, screen_height)
                    )
                else:
                    # 如果图片高度超过屏幕，允许滚动，但图片保持居中
                    scroll_height = new_height + screen_height
                    self.second_canvas.configure(
                        scrollregion=(0, 0, screen_width, scroll_height)
                    )
            else:
                # 正常模式保持原有逻辑 - 允许将底部内容拉到顶部显示
                if new_height > screen_height:
                    scroll_height = (
                        new_height + screen_height
                    )  # 图片高度 + 一个屏幕高度的额外空间
                else:
                    scroll_height = screen_height
                self.second_canvas.configure(
                    scrollregion=(0, 0, screen_width, scroll_height)
                )
            
            return True
            
        except Exception as e:
            print(f"在投影窗口显示图片失败: {e}")
            return False
    
    def sync_projection_screen_absolute(self):
        """使用绝对像素位置同步投影屏幕，解决滚动不一致问题
        
        Returns:
            bool: 同步是否成功
        """
        if not self.second_canvas or not self.sync_enabled or not self.main_app.image:
            return False

        try:
            # 性能节流：避免过于频繁的同步
            current_time = time.time()
            if current_time - self._last_sync_time < self._sync_throttle_interval:
                return True
            self._last_sync_time = current_time

            # 获取主屏幕当前的绝对像素位置
            main_scroll_top = self.main_app.canvas.canvasy(0)  # 主屏幕顶部对应的图片像素位置

            # 获取主屏幕的图片尺寸信息
            main_width, main_height = self.get_effective_screen_size(
                is_projection_screen=False
            )

            # 计算主屏幕图片的实际高度
            if self.main_app.original_mode:
                main_width_ratio = main_width / self.main_app.image.width
                main_height_ratio = main_height / self.main_app.image.height
                if main_width_ratio < 1 or main_height_ratio < 1:
                    main_scale = min(main_width_ratio, main_height_ratio)
                    main_img_height = int(self.main_app.image.height * main_scale)
                else:
                    main_img_height = self.main_app.image.height
            else:
                main_base_ratio = main_width / self.main_app.image.width
                main_final_ratio = main_base_ratio * self.main_app.zoom_ratio
                main_img_height = int(self.main_app.image.height * main_final_ratio)

            # 获取投影屏幕的图片尺寸信息
            proj_width, proj_height = self.get_effective_screen_size(
                is_projection_screen=True
            )

            # 计算投影屏幕图片的实际高度
            if self.main_app.original_mode:
                proj_width_ratio = proj_width / self.main_app.image.width
                proj_height_ratio = proj_height / self.main_app.image.height
                if proj_width_ratio < 1 or proj_height_ratio < 1:
                    proj_scale = min(proj_width_ratio, proj_height_ratio)
                    proj_img_height = int(self.main_app.image.height * proj_scale)
                else:
                    proj_img_height = self.main_app.image.height
            else:
                proj_base_ratio = proj_width / self.main_app.image.width
                proj_final_ratio = proj_base_ratio * self.main_app.zoom_ratio
                proj_img_height = int(self.main_app.image.height * proj_final_ratio)

            # 计算在原始图片上的相对位置（0-1之间）
            original_relative_pos = (
                main_scroll_top / main_img_height if main_img_height > 0 else 0
            )

            # 计算投影屏幕应该滚动到的绝对像素位置
            proj_scroll_top = original_relative_pos * proj_img_height

            # 获取投影屏幕的滚动区域信息
            proj_scroll_region = self.second_canvas.cget("scrollregion").split()
            if len(proj_scroll_region) >= 4:
                proj_scroll_height = float(proj_scroll_region[3])

                # 计算投影屏幕的相对位置
                proj_relative_pos = (
                    proj_scroll_top / proj_scroll_height
                    if proj_scroll_height > 0
                    else 0
                )

                # 限制在有效范围内
                proj_relative_pos = max(0, min(1, proj_relative_pos))

                # 应用到投影屏幕
                self.second_canvas.yview_moveto(proj_relative_pos)
                
                # 重置错误计数器
                self._sync_error_count = 0
                return True

        except Exception as e:
            self._sync_error_count += 1
            print(f"绝对位置同步失败 ({self._sync_error_count}/{self._max_error_count}): {e}")
            
            # 如果错误次数过多，降级到简单同步
            if self._sync_error_count < self._max_error_count:
                try:
                    # 降级到原来的同步方式
                    self.second_canvas.yview_moveto(self.main_app.canvas.yview()[0])
                    return True
                except Exception as fallback_error:
                    print(f"降级同步也失败: {fallback_error}")
            else:
                print("同步错误次数过多，暂时禁用同步")
                
        return False
    
    def update_projection(self):
        """更新投影屏幕
        
        Returns:
            bool: 更新是否成功
        """
        if not self.second_window or not self.main_app.image:
            return False
            
        try:
            screen = self.screens[self.main_app.screen_combo.current()]
            screen_width = screen.width
            screen_height = screen.height

            # 计算图片尺寸
            new_width, new_height = self._calculate_image_size(screen_width, screen_height)

            # 统一的实时处理逻辑
            resized_image = self.main_app.image.resize(
                (new_width, new_height), Image.Resampling.LANCZOS
            )

            # 根据模式应用效果
            if self.main_app.is_inverted:
                resized_image = self.main_app.apply_yellow_text_effect(resized_image)
            elif self.main_app.is_simple_inverted:
                resized_image = self.main_app.simple_invert_image(resized_image)

            self.second_photo = ImageTk.PhotoImage(resized_image)

            # 将图片放在顶部，水平居中 - 与show_on_second_screen保持一致
            x = max(0, (screen_width - new_width) // 2)

            # 优化：使用itemconfig更新投影屏图片，避免删除重建（减少闪烁）
            if hasattr(self, "second_image_on_canvas") and self.second_image_on_canvas:
                # 更新现有图片对象
                self.second_canvas.coords(self.second_image_on_canvas, x, 0)
                self.second_canvas.itemconfig(self.second_image_on_canvas, image=self.second_photo)
            else:
                # 首次创建图片对象
                self.second_image_on_canvas = self.second_canvas.create_image(
                    x, 0, anchor=tk.NW, image=self.second_photo
                )

            # 设置投影滚动区域 - 允许将底部内容拉到顶部显示
            if new_height > screen_height:
                scroll_height = (
                    new_height + screen_height
                )  # 图片高度 + 一个屏幕高度的额外空间
            else:
                scroll_height = screen_height

            self.second_canvas.configure(
                scrollregion=(0, 0, screen_width, scroll_height)
            )

            # 移除强制刷新，让系统自然更新（减少闪烁）
            # self.second_canvas.update_idletasks()
            
            return True

        except Exception as e:
            print(f"更新投影失败: {e}")
            return False
    
    def update_second_screen(self):
        """更新第二屏幕显示
        
        Returns:
            bool: 更新是否成功
        """
        if not self.second_window or not self.main_app.image:
            return False

        try:
            # 获取选定的屏幕
            selected = self.main_app.screen_combo.current()
            screen = self.screens[selected]

            # 获取实际窗口尺寸而不是屏幕尺寸
            screen_width = self.second_window.winfo_width()
            screen_height = self.second_window.winfo_height()

            # 确保获取到有效的窗口尺寸
            if screen_width <= 1 or screen_height <= 1:
                screen_width = screen.width
                screen_height = screen.height
                # 重新设置窗口大小以确保正确显示
                self.second_window.geometry(
                    f"{screen_width}x{screen_height}+{screen.x}+{screen.y}"
                )
                self.second_window.update_idletasks()
                # 再次尝试获取窗口尺寸
                screen_width = self.second_window.winfo_width() or screen.width
                screen_height = self.second_window.winfo_height() or screen.height

            # print(f"投影窗口实际尺寸: {screen_width}x{screen_height}")

            if self.main_app.original_mode:
                # 智能原图模式：根据屏幕分辨率智能缩放，最大化利用屏幕空间
                width_ratio = screen_width / self.main_app.image.width
                height_ratio = screen_height / self.main_app.image.height

                # 选择较小的缩放比例以确保完整显示
                scale_ratio = min(width_ratio, height_ratio)

                # 智能缩放策略：根据图片和屏幕的关系调整
                if scale_ratio < 1:
                    # 图片大于屏幕：缩小到适合屏幕
                    pass  # 使用计算出的scale_ratio
                else:
                    # 图片小于屏幕：智能放大以更好地利用屏幕空间
                    # 根据屏幕分辨率和图片大小动态调整最大放大倍数
                    screen_area = screen_width * screen_height
                    image_area = self.main_app.image.width * self.main_app.image.height
                    area_ratio = screen_area / image_area

                    # 动态计算最大放大倍数
                    if area_ratio > 16:  # 屏幕面积是图片的16倍以上
                        max_scale = 6.0  # 允许更大的放大倍数
                    elif area_ratio > 9:  # 屏幕面积是图片的9倍以上
                        max_scale = 4.0
                    elif area_ratio > 4:  # 屏幕面积是图片的4倍以上
                        max_scale = 3.0
                    else:
                        max_scale = 2.0  # 保守的放大倍数

                    # 应用最大放大限制
                    scale_ratio = min(scale_ratio, max_scale)

                new_width = int(self.main_app.image.width * scale_ratio)
                new_height = int(self.main_app.image.height * scale_ratio)

                print(
                    f"更新投影智能原图模式: 原尺寸({self.main_app.image.width}x{self.main_app.image.height}) -> 显示尺寸({new_width}x{new_height}) 缩放比例: {scale_ratio:.2f} 面积比: {area_ratio:.1f}"
                )
            else:
                # 正常模式下的缩放逻辑保持不变
                base_ratio = screen_width / self.main_app.image.width
                final_ratio = base_ratio * self.main_app.zoom_ratio
                new_width = int(self.main_app.image.width * final_ratio)
                new_height = int(self.main_app.image.height * final_ratio)

            # 统一的实时处理逻辑
            resized_image = self.main_app.image.resize(
                (new_width, new_height), Image.Resampling.LANCZOS
            )

            # 根据模式应用效果
            if self.main_app.is_inverted:
                resized_image = self.main_app.apply_yellow_text_effect(resized_image)
            elif self.main_app.is_simple_inverted:
                resized_image = self.main_app.simple_invert_image(resized_image)
            self.second_photo = ImageTk.PhotoImage(resized_image)

            # 将图片放在顶部，水平居中
            x = max(0, (screen_width - new_width) // 2)

            # 优化：使用itemconfig更新投影屏图片，避免删除重建（减少闪烁）
            if hasattr(self, "second_image_on_canvas") and self.second_image_on_canvas:
                # 更新现有图片对象
                self.second_canvas.coords(self.second_image_on_canvas, x, 0)
                self.second_canvas.itemconfig(self.second_image_on_canvas, image=self.second_photo)
            else:
                # 首次创建图片对象
                self.second_image_on_canvas = self.second_canvas.create_image(
                    x, 0, anchor=tk.NW, image=self.second_photo
                )

            # 设置投影窗口滚动区域 - 允许将底部内容拉到顶部显示
            if new_height > screen_height:
                scroll_height = (
                    new_height + screen_height
                )  # 图片高度 + 一个屏幕高度的额外空间
            else:
                scroll_height = screen_height

            self.second_canvas.configure(
                scrollregion=(0, 0, screen_width, scroll_height)
            )

            # 移除强制刷新，让系统自然更新（减少闪烁）
            # self.second_canvas.update_idletasks()
            
            return True

        except Exception as e:
            print(f"更新投影失败: {e}")
            return False
    
    def force_update_projection(self):
        """强制更新投影窗口
        
        Returns:
            bool: 更新是否成功
        """
        if not self.second_window or not self.main_app.image:
            return False
            
        try:
            # 获取选定的屏幕
            selected = self.main_app.screen_combo.current()
            screen = self.screens[selected]
            screen_width = screen.width
            screen_height = screen.height

            # 统一使用与第一张图片相同的计算方式
            new_width, new_height = self._calculate_image_size(screen_width, screen_height)

            # 统一的实时处理逻辑
            resized_image = self.main_app.image.resize(
                (new_width, new_height), Image.Resampling.LANCZOS
            )

            # 根据模式应用效果
            if self.main_app.is_inverted:
                resized_image = self.main_app.apply_yellow_text_effect(resized_image)
            elif self.main_app.is_simple_inverted:
                resized_image = self.main_app.simple_invert_image(resized_image)
            self.second_photo = ImageTk.PhotoImage(resized_image)

            # 将图片放在顶部，水平居中
            x = max(0, (screen_width - new_width) // 2)

            # 优化：使用itemconfig更新投影屏图片，避免删除重建（减少闪烁）
            if hasattr(self, "second_image_on_canvas") and self.second_image_on_canvas:
                # 更新现有图片对象
                self.second_canvas.coords(self.second_image_on_canvas, x, 0)
                self.second_canvas.itemconfig(self.second_image_on_canvas, image=self.second_photo)
            else:
                # 首次创建图片对象
                self.second_image_on_canvas = self.second_canvas.create_image(
                    x, 0, anchor=tk.NW, image=self.second_photo
                )

            # 设置投影滚动区域 - 允许将底部内容拉到顶部显示
            if new_height > screen_height:
                scroll_height = (
                    new_height + screen_height
                )  # 图片高度 + 一个屏幕高度的额外空间
            else:
                scroll_height = screen_height

            self.second_canvas.configure(
                scrollregion=(0, 0, screen_width, scroll_height)
            )

            # 移除强制刷新，让系统自然更新（减少闪烁）
            # self.second_canvas.update_idletasks()

            # print("投影窗口已强制更新")
            return True
            
        except Exception as e:
            print(f"强制更新投影失败: {e}")
            return False
    
    def create_monitor_menu(self, parent_frame):
        """创建显示器选择菜单
        
        Args:
            parent_frame: 父容器框架
            
        Returns:
            tuple: (monitor_frame, monitor_select) 创建的组件
        """
        try:
            # 创建一个Frame来容纳标签和下拉框
            monitor_frame = tk.Frame(parent_frame)
            monitor_frame.pack(side=tk.LEFT, padx=5)

            # 创建标签
            label = tk.Label(monitor_frame, text="显示器:")
            label.pack(side=tk.LEFT)

            monitor_var = tk.StringVar()
            monitors = self.get_monitor_info()

            # 创建选择框
            monitor_select = tk.OptionMenu(
                monitor_frame,
                monitor_var,
                *[m["name"] for m in monitors]
            )
            monitor_select.config(width=10)

            # 设置默认值为第一个非主显示器（如果有的话）
            default_index = 0
            for i, m in enumerate(monitors):
                if not m["is_primary"] and len(monitors) > 1:
                    default_index = i
                    break
            
            if monitors:
                monitor_var.set(monitors[default_index]["name"])

            monitor_select.pack(side=tk.LEFT, padx=5)
            
            # 存储引用供后续使用
            self._monitor_var = monitor_var
            self._monitor_select = monitor_select
            self._monitors = monitors
            
            print(f"显示器选择菜单创建成功，包含 {len(monitors)} 个显示器")
            return monitor_frame, monitor_select
            
        except Exception as e:
            print(f"创建显示器选择菜单失败: {e}")
            return None, None
    
    def get_selected_monitor_index(self):
        """获取当前选择的显示器索引
        
        Returns:
            int: 选择的显示器索引，如果出错返回0
        """
        try:
            if hasattr(self, '_monitor_var') and hasattr(self, '_monitors'):
                selected_name = self._monitor_var.get()
                for i, monitor in enumerate(self._monitors):
                    if monitor["name"] == selected_name:
                        return i
            return 0
        except Exception as e:
            print(f"获取选择的显示器索引失败: {e}")
            return 0
    
    # ==================== GPU加速相关方法 ====================
    
    def sync_with_gpu(self, gpu_engine):
        """与GPU引擎同步投影状态
        
        Args:
            gpu_engine: GPU滚动引擎实例
            
        Returns:
            bool: 同步是否成功
        """
        if not (self.second_window and self.second_canvas and self.sync_enabled):
            return False
        
        try:
            # 直接使用GPU引擎的当前位置
            current_pos = gpu_engine.current_scroll_pos
            
            # 同步投影屏幕位置
            self.second_canvas.yview_moveto(current_pos)
            
            return True
        except Exception as e:
            print(f"GPU同步投影失败: {e}")
            return False
    
    def enable_gpu_projection(self, enable=True):
        """启用或禁用GPU投影直接渲染
        
        Args:
            enable (bool): 是否启用GPU投影
            
        Returns:
            bool: 操作是否成功
        """
        if not GPU_PROJECTION_AVAILABLE:
            print("❌ GPU投影渲染器不可用")
            return False
            
        try:
            if enable and not self.use_gpu_projection:
                # 获取当前选定的屏幕信息
                selected = self.main_app.screen_combo.current()
                if selected < 0 or selected >= len(self.screens):
                    print("❌ 无效的屏幕选择")
                    return False
                    
                screen = self.screens[selected]
                screen_info = {
                    'x': screen.x,
                    'y': screen.y,
                    'width': screen.width,
                    'height': screen.height
                }
                
                # 获取主GPU桥接器（用于纹理共享）
                main_gpu_bridge = None
                if (hasattr(self.main_app, 'scroll_engine') and 
                    hasattr(self.main_app.scroll_engine, 'gpu_bridge')):
                    main_gpu_bridge = self.main_app.scroll_engine.gpu_bridge
                
                # 创建GPU投影渲染器
                self.gpu_projection_renderer = create_gpu_projection_renderer(
                    screen_info, main_gpu_bridge
                )
                
                if self.gpu_projection_renderer:
                    self.use_gpu_projection = True
                    print("🚀 GPU投影直接渲染已启用")
                    return True
                else:
                    print("❌ GPU投影渲染器创建失败")
                    return False
                    
            elif not enable and self.use_gpu_projection:
                # 禁用GPU投影
                if self.gpu_projection_renderer:
                    self.gpu_projection_renderer.stop_rendering()
                    self.gpu_projection_renderer = None
                self.use_gpu_projection = False
                print("✅ GPU投影直接渲染已禁用")
                return True
                
            return True
            
        except Exception as e:
            print(f"❌ 切换GPU投影模式失败: {e}")
            return False
    
    def start_gpu_projection(self, image_path):
        """启动GPU投影直接渲染
        
        Args:
            image_path: 图片路径
            
        Returns:
            bool: 启动是否成功
        """
        if not self.use_gpu_projection or not self.gpu_projection_renderer:
            return False
            
        try:
            # 设置要显示的图片
            if self.gpu_projection_renderer.set_image(image_path):
                # 启动渲染循环
                if self.gpu_projection_renderer.start_rendering():
                    print(f"🚀 GPU投影直接渲染已启动: {image_path}")
                    return True
                else:
                    print("❌ GPU投影渲染循环启动失败")
                    return False
            else:
                print("❌ GPU投影图片设置失败")
                return False
                
        except Exception as e:
            print(f"❌ 启动GPU投影失败: {e}")
            return False
    
    def update_gpu_projection_scroll(self, position):
        """更新GPU投影的滚动位置
        
        Args:
            position: 滚动位置 (0.0-1.0)
        """
        if self.use_gpu_projection and self.gpu_projection_renderer:
            try:
                self.gpu_projection_renderer.set_scroll_position(position)
            except Exception as e:
                print(f"❌ 更新GPU投影滚动位置失败: {e}")
    
    def apply_gpu_projection_effects(self, invert_colors=False, yellow_text_mode=False,
                                   yellow_color=(1.0, 1.0, 0.0), alpha=1.0):
        """应用GPU投影视觉效果
        
        Args:
            invert_colors: 是否反色
            yellow_text_mode: 是否黄字模式
            yellow_color: 黄字颜色
            alpha: 透明度
        """
        if self.use_gpu_projection and self.gpu_projection_renderer:
            try:
                # GPU渲染器会在每一帧自动应用这些效果
                # 这里可以设置渲染参数
                pass
            except Exception as e:
                print(f"❌ 应用GPU投影效果失败: {e}")
    
    def stop_gpu_projection(self):
        """停止GPU投影直接渲染"""
        if self.use_gpu_projection and self.gpu_projection_renderer:
            try:
                self.gpu_projection_renderer.stop_rendering()
                print("✅ GPU投影直接渲染已停止")
            except Exception as e:
                print(f"❌ 停止GPU投影失败: {e}")
    



# ==================== 工具函数 ====================

def get_projection_manager_version():
    """获取投影管理器版本信息"""
    return "V1.9.8"


def test_projection_manager():
    """测试投影管理器功能"""
    print("投影管理器测试功能 - 仅用于开发调试")
    
    # 创建一个简单的测试对象
    class MockMainApp:
        def __init__(self):
            self.root = None
            self.canvas = None
            self.image = None
            self.screen_combo = None
            self.btn_show = None
            self.original_mode = False
            self.zoom_ratio = 1.0
            self.is_inverted = False
            self.is_simple_inverted = False
    
    mock_app = MockMainApp()
    projection_manager = ProjectionManager(mock_app)
    
    # 测试基本功能
    print(f"投影管理器版本: {get_projection_manager_version()}")
    print(f"检测到屏幕数量: {len(projection_manager.screens)}")
    print("基础功能测试完成")


if __name__ == "__main__":
    # 如果直接运行此文件，执行测试
    test_projection_manager()