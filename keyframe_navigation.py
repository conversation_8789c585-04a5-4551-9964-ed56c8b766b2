"""
关键帧导航扩展模块
包含复杂的关键帧导航逻辑，如前进、后退、智能跳转等
"""

import time


class KeyframeNavigation:
    """关键帧导航扩展类"""
    
    def __init__(self, keyframe_manager):
        """初始化关键帧导航
        
        Args:
            keyframe_manager: 关键帧管理器实例
        """
        self.km = keyframe_manager
        self.app = keyframe_manager.app
    
    def step_to_prev_keyframe(self):
        """执行前一个关键帧"""
        # 特殊处理：如果当前有滚动动画正在进行，立即停止并强制直接跳转
        if hasattr(self.app, "scroll_engine") and self.app.scroll_engine.is_running():
            self.app.scroll_engine.stop_animation()
            print("检测到滚动动画正在进行，立即停止并直接跳转")
            # 强制使用直接跳转模式
            force_direct_jump = True
        else:
            force_direct_jump = False

        # 移除原图模式下切换图片的逻辑，恢复关键帧功能
        if not hasattr(self.app, "current_image_id"):
            return

        keyframes = self.km.get_keyframes(self.app.current_image_id)
        if not keyframes:
            return

        # 播放时间修正逻辑：如果正在播放，记录手动操作进行时间修正
        if (
            self.app.is_auto_playing
            and self.app.auto_player
            and self.km.current_keyframe_index >= 0
        ):
            current_keyframe_id = keyframes[self.km.current_keyframe_index][0]
            self.app.auto_player.record_manual_operation(current_keyframe_id)

        # 时间录制逻辑：记录当前关键帧的停留时间（在切换前记录）
        if (
            self.app.is_recording_timing
            and self.app.time_recorder
            and self.km.current_keyframe_index >= 0
        ):
            # 获取当前关键帧的ID
            current_keyframe_id = keyframes[self.km.current_keyframe_index][0]
            self.app.time_recorder.record_keyframe_timing(current_keyframe_id)

        # 记录操作历史
        current_time = time.time()
        current_index = self.km.current_keyframe_index
        self.km.keyframe_operation_history.append(
            {"type": "prev", "from_index": current_index, "time": current_time}
        )
        self.km.last_operation_time = current_time

        # 取消任何待定的跳转检查
        if hasattr(self.km, "_jump_check_timer"):
            self.app.root.after_cancel(self.km._jump_check_timer)

        # 更新当前帧索引
        self.km.current_keyframe_index -= 1
        if self.km.current_keyframe_index < 0:
            # 直接跳转到最后一帧
            self.km.current_keyframe_index = len(keyframes) - 1
            _, target_position, _ = keyframes[self.km.current_keyframe_index]

            # 直接跳转到最后一帧
            self.app.canvas.yview_moveto(target_position)
            if self.app.second_window and self.app.sync_enabled:
                self.app.sync_projection_screen_absolute()

            # 批量更新UI（减少重复调用）
            self.km._schedule_ui_update('both')

            print("已到达第一帧，直接跳转到最后一帧")
            return

        # 获取目标位置
        _, target_position, _ = keyframes[self.km.current_keyframe_index]

        # 检查滚动时间，如果为0或强制直接跳转则直接跳转，否则平滑滚动
        if self.km.scroll_duration == 0 or force_direct_jump:
            # 取消任何正在进行的滚动动画
            if hasattr(self.app, "_scroll_animation_id"):
                self.app.root.after_cancel(self.app._scroll_animation_id)

            # 直接设置位置
            self.app.canvas.yview_moveto(target_position)

            # 同步投影（直接跳转需要实时同步）
            if self.app.second_window and self.app.sync_enabled:
                self.app.sync_projection_screen_absolute()

            # 更新预览线（0秒跳转时需要手动更新）
            self.km.update_preview_lines()

            if force_direct_jump:
                print("强制直接跳转到上一关键帧")
        else:
            # 平滑滚动到该位置
            self.km.smooth_scroll_to(target_position)

        # 更新指示器（使用防抖）
        self.km._schedule_ui_update('indicators')
    
    def step_to_next_keyframe(self):
        """执行下一个关键帧"""
        # 特殊处理：如果当前有滚动动画正在进行，立即停止并强制直接跳转
        if hasattr(self.app, "scroll_engine") and self.app.scroll_engine.is_running():
            self.app.scroll_engine.stop_animation()
            print("检测到滚动动画正在进行，立即停止并直接跳转")
            # 强制使用直接跳转模式
            force_direct_jump = True
        else:
            force_direct_jump = False

        # 移除原图模式下切换图片的逻辑，恢复关键帧功能
        if not hasattr(self.app, "current_image_id"):
            return

        keyframes = self.km.get_keyframes(self.app.current_image_id)
        if not keyframes:
            return

        # 播放时间修正逻辑：如果正在播放，记录手动操作进行时间修正
        if (
            self.app.is_auto_playing
            and self.app.auto_player
            and self.km.current_keyframe_index >= 0
        ):
            current_keyframe_id = keyframes[self.km.current_keyframe_index][0]
            self.app.auto_player.record_manual_operation(current_keyframe_id)

            # 手动跳转后需要重新启动倒计时，跳转到下一帧
            # 取消当前的播放定时器和倒计时
            if self.app.auto_player.play_timer_id:
                self.app.root.after_cancel(self.app.auto_player.play_timer_id)
                self.app.auto_player.play_timer_id = None
            if self.app.auto_player.countdown_timer_id:
                self.app.root.after_cancel(self.app.auto_player.countdown_timer_id)
                self.app.auto_player.countdown_timer_id = None

            # 立即触发下一帧播放，这会重新启动倒计时（激进优化：1ms延迟）
            self.app.root.after(1, self.app.auto_player._play_next_frame)

        # 时间录制逻辑：记录当前关键帧的停留时间
        if (
            self.app.is_recording_timing
            and self.app.time_recorder
            and self.km.current_keyframe_index >= 0
        ):
            # 获取当前关键帧的ID
            current_keyframe_id = keyframes[self.km.current_keyframe_index][0]
            self.app.time_recorder.record_keyframe_timing(current_keyframe_id)

        # 获取当前关键帧索引
        current_index = self.km.current_keyframe_index

        # 记录操作历史
        current_time = time.time()
        self.km.keyframe_operation_history.append(
            {"type": "next", "from_index": current_index, "time": current_time}
        )
        self.km.last_operation_time = current_time

        # 取消任何待定的跳转检查（用户还在操作）
        if hasattr(self.km, "_jump_check_timer"):
            self.app.root.after_cancel(self.km._jump_check_timer)

        # 更新当前帧索引
        self.km.current_keyframe_index += 1

        # 检查是否超出范围
        if self.km.current_keyframe_index >= len(keyframes):
            # 已经超出范围，直接跳转到第一帧
            self.km.current_keyframe_index = 0
            _, target_position, _ = keyframes[self.km.current_keyframe_index]

            # 新增：如果正在录制，自动停止录制
            if self.app.is_recording_timing:
                self.app.toggle_timing_recording()

            # 直接跳转到第一帧
            self.app.canvas.yview_moveto(target_position)
            if self.app.second_window and self.app.sync_enabled:
                self.app.sync_projection_screen_absolute()

            # 批量更新UI（减少重复调用）
            self.km._schedule_ui_update('both')

            return
        else:
            is_looping_back = False

        # 检查是否是首次执行（之前未播放过关键帧）
        is_first_execution = current_index == -1

        # 如果是循环回第一帧或者是首次执行，使用直接跳转
        if is_looping_back or is_first_execution:
            # 设置到第一帧 - 使用直接跳转
            self.km.current_keyframe_index = 0
            _, target_position, _ = keyframes[self.km.current_keyframe_index]

            # 直接设置位置而不是平滑滚动
            self.app.canvas.yview_moveto(target_position)
            if self.app.second_window and self.app.sync_enabled:
                self.app.sync_projection_screen_absolute()
        else:
            # 正常情况下滚动到下一帧
            _, target_position, _ = keyframes[self.km.current_keyframe_index]

            # 检查滚动时间，如果为0或强制直接跳转则直接跳转，否则平滑滚动
            if self.km.scroll_duration == 0 or force_direct_jump:
                # 取消任何正在进行的滚动动画
                if hasattr(self.app, "_scroll_animation_id"):
                    self.app.root.after_cancel(self.app._scroll_animation_id)

                # 直接设置位置
                self.app.canvas.yview_moveto(target_position)

                # 同步投影
                if self.app.second_window and self.app.sync_enabled:
                    self.app.sync_projection_screen_absolute()

                # 更新预览线（0秒跳转时需要手动更新）
                self.km.update_preview_lines()

                if force_direct_jump:
                    print("强制直接跳转到下一关键帧")
            else:
                # 平滑滚动到该位置
                self.km.smooth_scroll_to(target_position)

        # 批量更新UI（减少重复调用）
        self.km._schedule_ui_update('both')
