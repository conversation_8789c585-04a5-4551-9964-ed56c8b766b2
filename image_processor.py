"""
图片处理模块 - ImageProcessor
负责图片加载、缓存管理、图片效果处理、缩放显示等功能

主要功能:
1. 图片加载和缓存管理
2. 图片显示和更新逻辑  
3. 图片效果处理 (黄字效果、反色)
4. 滚动处理和同步
5. 图片保存功能
6. 缩放和质量控制
"""

import tkinter as tk
from tkinter import filedialog, messagebox
from PIL import Image, ImageTk, ImageOps
import numpy as np
from cachetools import LRUCache
from pathlib import Path
import time


class ImageProcessor:
    """图片处理管理器
    
    负责管理图片加载、缓存、效果处理、显示更新等功能
    """
    
    def __init__(self, main_app):
        """初始化图片处理器
        
        Args:
            main_app: 主应用程序实例
        """
        self.main_app = main_app
        
        # 图片状态
        self.current_image = None
        self.current_photo = None
        self.image_on_canvas = None
        
        # 缓存管理
        self.image_cache = ImageCache()
        
        # 效果状态
        self.is_inverted = False
        self.is_simple_inverted = False
        
        # 缩放状态
        self.zoom_ratio = 1.0
        self.zoom_step = 1.1
        
        # 性能优化
        self._last_update_time = 0
        self._update_throttle_interval = 0.016  # 约60FPS
        self._last_scroll_time = 0
        
        # 初始化设置
        self._init_image_settings()
        
    def _init_image_settings(self):
        """初始化图片设置"""
        # 设置默认图片处理参数
        self._max_image_size = (8192, 8192)  # 最大图片尺寸限制
        self._memory_limit_mb = 200  # 内存限制 (MB)
        self._cache_enabled = True
        
        # 图片质量设置
        self._jpeg_quality = 95
        self._png_optimize = True
        
        # 性能监控
        self._performance_stats = {
            'load_count': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'total_load_time': 0.0
        }
        
        # print("图片处理器初始化完成")
        
    # ==================== 图片加载功能 ====================
    
    def load_image(self, path):
        """优化的图片加载方法
        
        Args:
            path: 图片路径 (可能包含虚拟路径标识)
            
        Returns:
            bool: 加载是否成功
        """
        start_time = time.time()
        
        try:
            # 更新统计
            self._performance_stats['load_count'] += 1
            
            # 处理虚拟路径：如果路径包含根目录标识，提取真实路径
            real_path = path
            if "#root_" in path:
                real_path = path.split("#root_")[0]

            # 清除当前图片
            self.clear_current_image()

            # 验证文件
            if not self._validate_image_file(real_path):
                raise ValueError(f"无效的图片文件: {real_path}")

            # 使用draft模式优化JPEG加载
            image = self._load_image_optimized(real_path)
            
            if not image:
                raise RuntimeError("图片加载失败")

            # 设置当前图片
            self.current_image = image
            
            # 记录图片路径（保存原始路径，包括虚拟路径标识）
            self.main_app.current_path = path

            # 更新图片显示
            success = self.update_image()
            
            if success:
                # 处理相关状态更新
                self._handle_post_load_actions(path)
                
                # 更新性能统计
                elapsed = time.time() - start_time
                self._performance_stats['total_load_time'] += elapsed
                
                if elapsed > 1.0:  # 如果加载耗时超过1秒
                    print(f"图片加载耗时: {elapsed:.3f}秒")
                
                return True
            else:
                return False

        except Exception as e:
            self._handle_image_error(f"加载图片失败: {str(e)}", path)
            return False
    
    def _validate_image_file(self, path):
        """验证图片文件
        
        Args:
            path: 图片文件路径
            
        Returns:
            bool: 文件是否有效
        """
        try:
            path_obj = Path(path)
            
            # 检查文件存在性
            if not path_obj.exists():
                return False
            
            # 检查文件大小
            file_size = path_obj.stat().st_size
            if file_size > 100 * 1024 * 1024:  # 100MB限制
                raise ValueError("图片文件过大 (>100MB)")
            
            if file_size == 0:  # 空文件
                return False
            
            # 检查文件格式
            valid_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tif', '.tiff', '.webp'}
            if path_obj.suffix.lower() not in valid_extensions:
                raise ValueError("不支持的图片格式")
            
            return True
            
        except Exception as e:
            print(f"验证图片文件失败: {e}")
            return False
    
    def _load_image_optimized(self, real_path):
        """优化的图片加载实现
        
        Args:
            real_path: 真实图片路径
            
        Returns:
            PIL.Image: 加载的图片或None
        """
        try:
            # 使用draft模式优化JPEG加载
            try:
                with Image.open(real_path) as temp_img:
                    # 计算合适的draft尺寸（预估显示尺寸的1.5倍以保证质量）
                    canvas_width, canvas_height = self.main_app.get_effective_screen_size(
                        is_projection_screen=False
                    )
                    draft_size = (int(canvas_width * 1.5), int(canvas_height * 1.5))

                    # 对于JPEG文件使用draft模式优化加载
                    if real_path.lower().endswith((".jpg", ".jpeg")):
                        temp_img.draft("RGB", draft_size)

                    # 加载图片
                    image = temp_img.copy()
                    
            except Exception:
                # 如果draft失败，使用常规方式加载
                image = Image.open(real_path)

            # 确保图片是RGB模式（兼容性处理）
            if image.mode != 'RGB':
                image = image.convert('RGB')
                
            # 检查图片尺寸限制
            if (image.width > self._max_image_size[0] or 
                image.height > self._max_image_size[1]):
                print(f"警告: 图片尺寸过大 ({image.width}x{image.height})")
                
            return image
            
        except Exception as e:
            print(f"优化加载图片失败: {e}")
            return None
    
    def _handle_post_load_actions(self, path):
        """处理图片加载后的相关动作
        
        Args:
            path: 图片路径
        """
        try:
            # 获取图片ID
            if hasattr(self.main_app, 'safe_db_execute'):
                result = self.main_app.safe_db_execute(
                    "SELECT id FROM images WHERE path = ?", (path,), fetch="one"
                )
                if result:
                    self.main_app.current_image_id = result[0]

                    # 在原图模式下，查找相似图片
                    if (hasattr(self.main_app, 'original_mode') and 
                        self.main_app.original_mode and
                        hasattr(self.main_app, 'yuantu_manager')):
                        self.main_app.yuantu_manager.find_similar_images(self.main_app.current_image_id)

            # 重置滚动条到顶部
            if hasattr(self.main_app, 'canvas'):
                self.main_app.canvas.yview_moveto(0)

            # 重置关键帧索引
            if hasattr(self.main_app, 'current_keyframe_index'):
                self.main_app.current_keyframe_index = -1

            # 更新关键帧指示器
            if hasattr(self.main_app, 'update_keyframe_indicators'):
                self.main_app.update_keyframe_indicators()

            # 更新预览线
            if hasattr(self.main_app, 'update_preview_lines'):
                self.main_app.update_preview_lines()

            # 如果投影窗口已打开，更新投影
            if (hasattr(self.main_app, 'projection_manager') and
                self.main_app.projection_manager.second_window and 
                self.main_app.projection_manager.sync_enabled):
                self.main_app.projection_manager.force_update_projection()
                # 同时重置投影窗口的滚动条
                self.main_app.projection_manager.second_canvas.yview_moveto(0)

            # 延迟更新按钮状态，确保图片加载完成后按钮状态正确
            if hasattr(self.main_app, 'update_button_status') and hasattr(self.main_app, 'root'):
                self.main_app.root.after(100, self.main_app.update_button_status)
                
        except Exception as e:
            print(f"处理图片加载后动作失败: {e}")
    
    def clear_current_image(self):
        """清除当前图片和相关状态"""
        try:
            # 清除画布
            if (hasattr(self.main_app, 'image_on_canvas') and 
                self.main_app.image_on_canvas and
                hasattr(self.main_app, 'canvas')):
                self.main_app.canvas.delete(self.main_app.image_on_canvas)
                self.main_app.image_on_canvas = None
                self.image_on_canvas = None

            # 清除图片对象
            self.current_image = None
            self.current_photo = None
            
            # 清除主应用的图片状态
            if hasattr(self.main_app, 'image'):
                self.main_app.image = None
            if hasattr(self.main_app, 'photo'):
                self.main_app.photo = None

            # 清除缓存
            self.image_cache.clear()

            # 清除关键帧状态
            if hasattr(self.main_app, 'current_image_id'):
                self.main_app.current_image_id = None
            if hasattr(self.main_app, 'current_keyframe_index'):
                self.main_app.current_keyframe_index = -1
            
            # 更新关键帧指示器
            if (hasattr(self.main_app, "indicator_frame") and 
                hasattr(self.main_app, 'update_keyframe_indicators')):
                self.main_app.update_keyframe_indicators()

            # 重置当前路径
            if hasattr(self.main_app, 'current_path'):
                self.main_app.current_path = None
                
        except Exception as e:
            print(f"清除当前图片失败: {e}")
    
    def get_image_info(self):
        """获取当前图片信息
        
        Returns:
            dict: 图片信息或None
        """
        if self.current_image:
            return {
                'width': self.current_image.width,
                'height': self.current_image.height,
                'mode': self.current_image.mode,
                'format': getattr(self.current_image, 'format', 'Unknown'),
                'size_bytes': getattr(self.current_image, 'size', 0)
            }
        return None
    
    def _handle_image_error(self, error_message, context=None):
        """处理图片相关错误
        
        Args:
            error_message: 错误信息
            context: 错误上下文
        """
        print(f"图片处理错误: {error_message}")
        if context:
            print(f"错误上下文: {context}")
        
        # 显示用户友好的错误信息
        if hasattr(self.main_app, 'show_error'):
            self.main_app.show_error(error_message)
        else:
            # 备用的错误显示方式
            messagebox.showerror("图片加载错误", error_message)
    
    def get_performance_stats(self):
        """获取性能统计信息
        
        Returns:
            dict: 性能统计数据
        """
        stats = self._performance_stats.copy()
        
        if stats['load_count'] > 0:
            stats['avg_load_time'] = stats['total_load_time'] / stats['load_count']
        else:
            stats['avg_load_time'] = 0
            
        # 添加缓存统计
        cache_stats = self.image_cache.get_cache_stats()
        stats.update(cache_stats)
        
        return stats
    
    # ==================== 图片显示和更新逻辑 ====================
    
    def update_image(self):
        """更新图片显示 - 修复与投影屏幕的显示一致性
        
        Returns:
            bool: 更新是否成功
        """
        if not self.current_image:
            return False

        try:
            # 性能节流
            current_time = time.time()
            if current_time - self._last_update_time < self._update_throttle_interval:
                return True
            self._last_update_time = current_time
            
            # 使用统一的尺寸计算方式，确保与投影屏幕一致
            canvas_width, canvas_height = self.main_app.get_effective_screen_size(
                is_projection_screen=False
            )

            # 计算显示尺寸
            new_width, new_height = self._calculate_display_size(canvas_width, canvas_height)

            # 获取或创建缓存图片
            cached_photo = self._get_or_create_cached_image(new_width, new_height)
            
            if not cached_photo:
                return False

            # 更新画布显示
            success = self._update_canvas_display(cached_photo, new_width, new_height, canvas_width, canvas_height)
            
            if success:
                # 更新关键帧指示器
                if hasattr(self.main_app, 'update_keyframe_indicators'):
                    self.main_app.update_keyframe_indicators()

                # 更新预览线
                if hasattr(self.main_app, 'update_preview_lines'):
                    self.main_app.update_preview_lines()
                    
            return success

        except Exception as e:
            print(f"更新图片失败: {e}")
            return False

    def _calculate_display_size(self, canvas_width, canvas_height):
        """计算图片显示尺寸
        
        Args:
            canvas_width: 画布宽度
            canvas_height: 画布高度
            
        Returns:
            tuple: (new_width, new_height)
        """
        if hasattr(self.main_app, 'original_mode') and self.main_app.original_mode:
            # 原图模式：根据显示模式选择缩放策略
            width_ratio = canvas_width / self.current_image.width
            height_ratio = canvas_height / self.current_image.height

            if (hasattr(self.main_app, 'original_display_mode') and 
                self.main_app.original_display_mode == 'stretch'):
                # 拉伸模式：高度按比例缩放确保内容完整，宽度拉伸填满画布
                scale_ratio = height_ratio  # 使用高度比例确保内容完整
            else:
                # 适中模式：选择较小的缩放比例以确保完整显示
                scale_ratio = min(width_ratio, height_ratio)

            # 智能缩放策略：根据图片和屏幕的关系调整
            if scale_ratio < 1:
                # 图片大于屏幕：缩小到适合屏幕
                pass  # 使用计算出的scale_ratio
            else:
                # 图片小于屏幕：智能放大以更好地利用屏幕空间
                # 根据屏幕分辨率和图片大小动态调整最大放大倍数
                screen_area = canvas_width * canvas_height
                image_area = self.current_image.width * self.current_image.height
                area_ratio = screen_area / image_area

                # 动态计算最大放大倍数
                if area_ratio > 16:  # 屏幕面积是图片的16倍以上
                    max_scale = 6.0  # 允许更大的放大倍数
                elif area_ratio > 9:  # 屏幕面积是图片的9倍以上
                    max_scale = 4.0
                elif area_ratio > 4:  # 屏幕面积是图片的4倍以上
                    max_scale = 3.0
                else:
                    max_scale = 2.0  # 保守的放大倍数

                # 应用最大放大限制
                scale_ratio = min(scale_ratio, max_scale)

            if (hasattr(self.main_app, 'original_mode') and self.main_app.original_mode and 
                hasattr(self.main_app, 'original_display_mode') and 
                self.main_app.original_display_mode == 'stretch'):
                # 拉伸模式：宽度填满画布，高度按比例缩放
                new_width = canvas_width
                new_height = int(self.current_image.height * scale_ratio)
            else:
                # 适中模式或正常模式：等比缩放
                new_width = int(self.current_image.width * scale_ratio)
                new_height = int(self.current_image.height * scale_ratio)

        else:
            # 正常模式下的缩放逻辑
            base_ratio = canvas_width / self.current_image.width
            final_ratio = base_ratio * self.zoom_ratio
            new_width = int(self.current_image.width * final_ratio)
            new_height = int(self.current_image.height * final_ratio)

        return new_width, new_height

    def _get_or_create_cached_image(self, new_width, new_height):
        """获取或创建缓存图片
        
        Args:
            new_width: 图片新宽度
            new_height: 图片新高度
            
        Returns:
            ImageTk.PhotoImage: 缓存的图片
        """
        # 检查普通图片缓存
        cached_photo = None
        cache_key = f"{new_width}x{new_height}_normal"

        # 只对普通模式使用缓存，效果模式直接实时处理
        if not self.is_inverted and not self.is_simple_inverted:
            if cache_key in self.image_cache:
                cached_photo = self.image_cache[cache_key]

        # 如果没有缓存，生成新的图片
        if cached_photo is None:
            resized_image = self._resize_and_apply_effects(new_width, new_height)
            if not resized_image:
                return None
                
            # 创建PhotoImage并缓存
            cached_photo = ImageTk.PhotoImage(resized_image)

            # 只缓存普通模式的图片，效果模式不缓存（避免内存浪费）
            if not self.is_inverted and not self.is_simple_inverted:
                cache_key = f"{new_width}x{new_height}_normal"
                self.image_cache[cache_key] = cached_photo

        return cached_photo

    def _resize_and_apply_effects(self, new_width, new_height):
        """调整图片尺寸并应用效果
        
        Args:
            new_width: 目标宽度
            new_height: 目标高度
            
        Returns:
            PIL.Image: 处理后的图片
        """
        try:
            # 使用reducing_gap参数优化resize性能
            reducing_gap = (
                2.0
                if min(new_width, new_height) < min(self.current_image.width, self.current_image.height)
                else None
            )

            # 统一的实时处理逻辑
            resized_image = self.current_image.resize(
                (new_width, new_height),
                Image.Resampling.LANCZOS,
                reducing_gap=reducing_gap,
            )

            # 根据模式应用效果
            if self.is_inverted:
                resized_image = self.apply_yellow_text_effect(resized_image)
            elif self.is_simple_inverted:
                resized_image = self.simple_invert_image(resized_image)

            return resized_image
            
        except Exception as e:
            print(f"调整图片尺寸失败: {e}")
            return None

    def _update_canvas_display(self, photo, new_width, new_height, canvas_width, canvas_height):
        """更新画布显示
        
        Args:
            photo: PhotoImage对象
            new_width: 图片宽度
            new_height: 图片高度
            canvas_width: 画布宽度
            canvas_height: 画布高度
            
        Returns:
            bool: 更新是否成功
        """
        try:
            # 使用缓存的图片
            self.current_photo = photo
            
            # 更新主应用的photo引用以保持兼容性
            if hasattr(self.main_app, 'photo'):
                self.main_app.photo = photo

            # 计算居中位置 - 水平和垂直都居中
            x = max(0, (canvas_width - new_width) // 2)
            y = max(0, (canvas_height - new_height) // 2)

            # 优化：使用itemconfig更新画布图片，避免删除重建（减少闪烁）
            if (hasattr(self.main_app, 'image_on_canvas') and 
                self.main_app.image_on_canvas and
                hasattr(self.main_app, 'canvas')):
                # 更新现有图片对象的位置和图像
                if hasattr(self.main_app, 'original_mode') and self.main_app.original_mode:
                    self.main_app.canvas.coords(self.main_app.image_on_canvas, x, y)
                else:
                    self.main_app.canvas.coords(self.main_app.image_on_canvas, x, 0)
                self.main_app.canvas.itemconfig(self.main_app.image_on_canvas, image=photo)
                self.image_on_canvas = self.main_app.image_on_canvas
            else:
                # 首次创建图片对象
                if hasattr(self.main_app, 'original_mode') and self.main_app.original_mode:
                    self.main_app.image_on_canvas = self.main_app.canvas.create_image(
                        x, y, anchor=tk.NW, image=photo
                    )
                else:
                    self.main_app.image_on_canvas = self.main_app.canvas.create_image(
                        x, 0, anchor=tk.NW, image=photo
                    )
                self.image_on_canvas = self.main_app.image_on_canvas
                
                # 在原图模式下，如果图片完全适合屏幕，则不需要滚动
                if new_height <= canvas_height:
                    self.main_app.canvas.configure(
                        scrollregion=(0, 0, canvas_width, canvas_height)
                    )
                else:
                    # 如果图片高度超过屏幕，允许滚动，但图片保持居中
                    scroll_height = new_height + canvas_height
                    self.main_app.canvas.configure(
                        scrollregion=(0, 0, canvas_width, scroll_height)
                    )

            # 统一设置滚动区域（原图模式外的情况）
            if not (hasattr(self.main_app, 'original_mode') and self.main_app.original_mode):
                if new_height > canvas_height:
                    scroll_height = new_height + canvas_height  # 图片高度 + 一个屏幕高度的额外空间
                else:
                    scroll_height = canvas_height
                self.main_app.canvas.configure(scrollregion=(0, 0, canvas_width, scroll_height))

            # 移除强制刷新，让系统自然更新（减少闪烁）
            # self.main_app.canvas.update_idletasks()
            
            return True

        except Exception as e:
            print(f"更新画布显示失败: {e}")
            return False

    def update_image_quality(self, width, height):
        """质量图片更新
        
        Args:
            width: 目标宽度
            height: 目标高度
        """
        if not hasattr(self.main_app, 'is_scrolling') or not self.main_app.is_scrolling:
            cache_key = f"{width}x{height}_{'inverted' if self.is_inverted else 'normal'}_{'simple_inverted' if self.is_simple_inverted else 'normal'}"

            if cache_key not in self.image_cache:
                # 统一的实时处理逻辑
                resized_image = self.current_image.resize(
                    (width, height), Image.Resampling.LANCZOS
                )

                # 根据模式应用效果
                if self.is_inverted:
                    resized_image = self.apply_yellow_text_effect(resized_image)
                elif self.is_simple_inverted:
                    resized_image = self.simple_invert_image(resized_image)

                self.image_cache[cache_key] = ImageTk.PhotoImage(resized_image)

            if (hasattr(self.main_app, 'image_on_canvas') and 
                self.main_app.image_on_canvas):
                self.main_app.canvas.itemconfig(
                    self.main_app.image_on_canvas, image=self.image_cache[cache_key]
                )
    
    # ==================== 滚动处理功能 ====================
    
    def on_scroll(self, *args):
        """优化的滚动处理
        
        Args:
            *args: 滚动参数
        """
        if not self.current_image:
            return
            
        try:
            current_time = time.time() * 1000
            
            # 更新主应用的滚动状态
            if hasattr(self.main_app, 'is_scrolling'):
                self.main_app.is_scrolling = True

            # 取消之前的定时器
            if hasattr(self.main_app, 'scroll_timer') and self.main_app.scroll_timer:
                self.main_app.root.after_cancel(self.main_app.scroll_timer)

            # 设置新的定时器
            if hasattr(self.main_app, 'root'):
                self.main_app.scroll_timer = self.main_app.root.after(100, self.on_scroll_end)

            # 主屏幕滚动
            if hasattr(self.main_app, 'canvas'):
                self.main_app.canvas.yview(*args)

            # 如果是反色模式或普通反色模式，确保滚动时保持反色效果
            if (self.is_inverted or self.is_simple_inverted) and self.current_image:
                self._update_scroll_effects()

            # 投影同步
            self._handle_projection_sync(current_time)

            # 更新预览线
            if hasattr(self.main_app, 'update_preview_lines'):
                self.main_app.update_preview_lines()
                
        except Exception as e:
            print(f"滚动处理失败: {e}")

    def _update_scroll_effects(self):
        """更新滚动时的效果"""
        try:
            # 使用统一的尺寸计算方式
            canvas_width, canvas_height = self.main_app.get_effective_screen_size(
                is_projection_screen=False
            )
            
            # 计算图片尺寸
            new_width, new_height = self._calculate_scroll_size(canvas_width, canvas_height)
            
            # 创建缓存键
            cache_key = f"{new_width}x{new_height}_{'inverted' if self.is_inverted else 'normal'}_{'simple_inverted' if self.is_simple_inverted else 'normal'}"

            if cache_key not in self.image_cache:
                # 统一的实时处理逻辑
                resized_image = self.current_image.resize(
                    (new_width, new_height), Image.Resampling.LANCZOS
                )

                # 根据模式应用效果
                if self.is_inverted:
                    resized_image = self.apply_yellow_text_effect(resized_image)
                elif self.is_simple_inverted:
                    resized_image = self.simple_invert_image(resized_image)

                self.image_cache[cache_key] = ImageTk.PhotoImage(resized_image)

            # 更新画布图片
            if (hasattr(self.main_app, 'image_on_canvas') and 
                self.main_app.image_on_canvas):
                self.main_app.canvas.itemconfig(
                    self.main_app.image_on_canvas, image=self.image_cache[cache_key]
                )
                
        except Exception as e:
            print(f"更新滚动效果失败: {e}")

    def _calculate_scroll_size(self, canvas_width, canvas_height):
        """计算滚动时的图片尺寸
        
        Args:
            canvas_width: 画布宽度
            canvas_height: 画布高度
            
        Returns:
            tuple: (new_width, new_height)
        """
        if hasattr(self.main_app, 'original_mode') and self.main_app.original_mode:
            # 智能原图模式：根据屏幕分辨率智能缩放，最大化利用屏幕空间
            width_ratio = canvas_width / self.current_image.width
            height_ratio = canvas_height / self.current_image.height
            scale_ratio = min(width_ratio, height_ratio)

            # 智能缩放策略：根据图片和屏幕的关系调整
            if scale_ratio < 1:
                # 图片大于屏幕：缩小到适合屏幕
                pass  # 使用计算出的scale_ratio
            else:
                # 图片小于屏幕：智能放大以更好地利用屏幕空间
                screen_area = canvas_width * canvas_height
                image_area = self.current_image.width * self.current_image.height
                area_ratio = screen_area / image_area

                # 动态计算最大放大倍数
                if area_ratio > 16:
                    max_scale = 6.0
                elif area_ratio > 9:
                    max_scale = 4.0
                elif area_ratio > 4:
                    max_scale = 3.0
                else:
                    max_scale = 2.0

                scale_ratio = min(scale_ratio, max_scale)

            new_width = int(self.current_image.width * scale_ratio)
            new_height = int(self.current_image.height * scale_ratio)
        else:
            base_ratio = canvas_width / self.current_image.width
            final_ratio = base_ratio * self.zoom_ratio
            new_width = int(self.current_image.width * final_ratio)
            new_height = int(self.current_image.height * final_ratio)

        return new_width, new_height

    def _handle_projection_sync(self, current_time):
        """处理投影同步
        
        Args:
            current_time: 当前时间
        """
        try:
            # 降低同步率，使用绝对位置同步
            if (hasattr(self.main_app, 'projection_manager') and
                self.main_app.projection_manager.second_canvas and 
                self.main_app.projection_manager.sync_enabled):
                self.main_app.projection_manager.sync_projection_screen_absolute()
                
                if hasattr(self.main_app, 'last_scroll_time'):
                    self.main_app.last_scroll_time = current_time
                    
        except Exception as e:
            print(f"投影同步失败: {e}")

    def on_scroll_end(self):
        """滚动结束处理"""
        try:
            # 更新主应用状态
            if hasattr(self.main_app, 'is_scrolling'):
                self.main_app.is_scrolling = False
            if hasattr(self.main_app, 'scroll_timer'):
                self.main_app.scroll_timer = None

            # 更新高质量图片
            if self.current_image:
                self._update_scroll_end_quality()
                
        except Exception as e:
            print(f"滚动结束处理失败: {e}")

    def _update_scroll_end_quality(self):
        """滚动结束后更新高质量图片"""
        try:
            # 使用统一的尺寸计算方式
            canvas_width, canvas_height = self.main_app.get_effective_screen_size(
                is_projection_screen=False
            )
            
            # 计算最终图片尺寸
            new_width, new_height = self._calculate_display_size(canvas_width, canvas_height)
            
            # 更新图片质量
            self.update_image_quality(new_width, new_height)
            
        except Exception as e:
            print(f"更新滚动结束质量失败: {e}")
    
    # ==================== 缩放功能 ====================
    
    def zoom_in(self):
        """放大图片"""
        try:
            self.zoom_ratio *= self.zoom_step
            self.zoom_ratio = min(self.zoom_ratio, 10.0)  # 最大10倍
            self._update_after_zoom()
            
        except Exception as e:
            print(f"放大失败: {e}")

    def zoom_out(self):
        """缩小图片"""
        try:
            self.zoom_ratio /= self.zoom_step
            self.zoom_ratio = max(self.zoom_ratio, 0.1)  # 最小0.1倍
            self._update_after_zoom()
            
        except Exception as e:
            print(f"缩小失败: {e}")

    def zoom_reset(self):
        """重置缩放"""
        try:
            self.zoom_ratio = 1.0
            self._update_after_zoom()
            
        except Exception as e:
            print(f"重置缩放失败: {e}")

    def _update_after_zoom(self):
        """缩放后更新"""
        try:
            # 清理缓存
            self.image_cache.clear()
            
            # 更新显示
            self.update_image()
            
            # 更新投影
            if (hasattr(self.main_app, 'projection_manager') and 
                self.main_app.projection_manager.second_window):
                self.main_app.projection_manager.force_update_projection()
            
            # 保存缩放设置
            if hasattr(self.main_app, 'save_config'):
                self.main_app.save_config()
                
        except Exception as e:
            print(f"缩放更新失败: {e}")
            
    def set_zoom_ratio(self, ratio):
        """设置缩放比例
        
        Args:
            ratio: 缩放比例
        """
        try:
            self.zoom_ratio = max(0.1, min(ratio, 10.0))
            self._update_after_zoom()
            
        except Exception as e:
            print(f"设置缩放比例失败: {e}")
            
    def get_zoom_ratio(self):
        """获取当前缩放比例
        
        Returns:
            float: 当前缩放比例
        """
        return self.zoom_ratio
    
    # ==================== 图片效果处理功能 ====================
    
    def apply_yellow_text_effect(self, image):
        """应用黄字效果（黑底黄字，底色强制纯黑）- 性能优化版本
        
        Args:
            image: PIL.Image对象
            
        Returns:
            PIL.Image: 处理后的图片
        """
        try:
            # 性能优化：检查图像是否已经是黄字效果
            if hasattr(image, "_is_yellow_processed") and image._is_yellow_processed:
                return image

            # 转换为RGBA模式，便于处理透明度
            if image.mode != "RGBA":
                image = image.convert("RGBA")
            img_array = np.array(image)

            # 性能优化：使用更快的亮度计算方法
            # 使用权重平均而不是简单平均，提高处理速度
            luminance = (
                0.299 * img_array[..., 0]
                + 0.587 * img_array[..., 1]
                + 0.114 * img_array[..., 2]
            )
            is_text = luminance < 150  # 阈值可调整

            # 性能优化：直接操作数组，减少内存分配
            result = np.zeros_like(img_array)
            result[..., 3] = 255  # 设置alpha通道

            # 使用配置的RGB颜色
            yellow_rgb = self._get_yellow_color_settings()
            result[is_text, 0] = yellow_rgb["r"]  # R
            result[is_text, 1] = yellow_rgb["g"]  # G
            result[is_text, 2] = yellow_rgb["b"]  # B
            result[is_text, 3] = img_array[is_text, 3]  # 保留原透明度

            # 保留原图透明区域
            transparent_mask = img_array[..., 3] == 0
            result[transparent_mask, 3] = 0

            final_image = Image.fromarray(result)
            # 标记图像已处理，避免重复处理
            final_image._is_yellow_processed = True
            return final_image
            
        except Exception as e:
            print(f"应用黄字效果失败: {e}")
            return image

    def simple_invert_image(self, image):
        """简单反转图片颜色
        
        Args:
            image: PIL.Image对象
            
        Returns:
            PIL.Image: 反色后的图片
        """
        try:
            # 转换为NumPy数组进行快速处理
            img_array = np.array(image)

            # 根据图像模式处理
            if len(img_array.shape) == 3:  # RGB或RGBA图像
                has_alpha = img_array.shape[2] == 4

                if has_alpha:
                    # 保存alpha通道
                    alpha = img_array[:, :, 3].copy()
                    # 反转RGB通道
                    inverted = np.zeros_like(img_array)
                    inverted[:, :, :3] = 255 - img_array[:, :, :3]
                    inverted[:, :, 3] = alpha  # 保持透明度不变
                else:
                    # 直接反转所有通道
                    inverted = 255 - img_array
            else:  # 灰度图像
                # 直接反转灰度值
                inverted = 255 - img_array

            # 转回PIL图像
            return Image.fromarray(inverted)
            
        except Exception as e:
            print(f"简单反色失败: {e}")
            return image

    def _get_yellow_color_settings(self):
        """获取黄字颜色设置
        
        Returns:
            dict: 黄字颜色RGB值
        """
        # 从主应用获取黄字颜色设置
        if (hasattr(self.main_app, 'yellow_text_rgb') and 
            self.main_app.yellow_text_rgb):
            return self.main_app.yellow_text_rgb
        
        # 默认黄字颜色
        return {"r": 174, "g": 159, "b": 112}

    def toggle_invert(self):
        """切换黄字效果状态"""
        try:
            # 如果当前是简单反色，先关闭
            if self.is_simple_inverted:
                self.is_simple_inverted = False
                if hasattr(self.main_app, 'btn_simple_invert'):
                    self.main_app.btn_simple_invert.configure(bg="#f0f0f0")

            # 切换黄字效果
            self.is_inverted = not self.is_inverted
            
            # 同步到主应用
            if hasattr(self.main_app, 'is_inverted'):
                self.main_app.is_inverted = self.is_inverted

            # 更新按钮状态
            if hasattr(self.main_app, 'btn_invert'):
                if self.is_inverted:
                    self.main_app.btn_invert.configure(bg="#90EE90")  # 浅绿色
                else:
                    self.main_app.btn_invert.configure(bg="#f0f0f0")  # 恢复默认颜色

            # 清理缓存并更新显示
            self.image_cache.clear()
            self.update_image()

            # 如果投影窗口已打开，更新投影
            if (hasattr(self.main_app, 'projection_manager') and
                self.main_app.projection_manager.second_window and 
                self.main_app.projection_manager.sync_enabled):
                self.main_app.projection_manager.update_projection()
                
        except Exception as e:
            print(f"切换黄字效果失败: {e}")

    def toggle_simple_invert(self):
        """切换简单反色状态"""
        try:
            # 如果黄字效果已启用，先关闭它
            if self.is_inverted:
                self.is_inverted = False
                if hasattr(self.main_app, 'btn_invert'):
                    self.main_app.btn_invert.configure(bg="#f0f0f0")
                if hasattr(self.main_app, 'is_inverted'):
                    self.main_app.is_inverted = False

            # 切换简单反色状态
            self.is_simple_inverted = not self.is_simple_inverted
            
            # 同步到主应用
            if hasattr(self.main_app, 'is_simple_inverted'):
                self.main_app.is_simple_inverted = self.is_simple_inverted

            # 更新按钮状态
            if hasattr(self.main_app, 'btn_simple_invert'):
                if self.is_simple_inverted:
                    self.main_app.btn_simple_invert.configure(bg="#90EE90")  # 浅绿色
                else:
                    self.main_app.btn_simple_invert.configure(bg="#f0f0f0")  # 恢复默认颜色

            # 清理缓存并更新显示
            self.image_cache.clear()
            if self.current_image:
                self.update_image()  # 更新主屏幕
                
                # 更新投影
                if (hasattr(self.main_app, 'projection_manager') and
                    self.main_app.projection_manager.second_window):
                    self.main_app.projection_manager.update_second_screen()
                    
        except Exception as e:
            print(f"切换简单反色失败: {e}")

    def update_yellow_color(self, color_rgb):
        """更新黄字颜色设置
        
        Args:
            color_rgb: 新的RGB颜色设置 {"r": r, "g": g, "b": b}
        """
        try:
            # 更新主应用的黄字颜色设置
            if hasattr(self.main_app, 'yellow_text_rgb'):
                self.main_app.yellow_text_rgb = color_rgb

            # 如果当前是黄字模式，更新显示
            if self.is_inverted:
                self.image_cache.clear()
                self.update_image()

                # 更新投影
                if (hasattr(self.main_app, 'projection_manager') and 
                    self.main_app.projection_manager.second_window):
                    self.main_app.projection_manager.force_update_projection()
                    
        except Exception as e:
            print(f"更新黄字颜色失败: {e}")

    def get_current_effect_mode(self):
        """获取当前效果模式
        
        Returns:
            str: 'normal', 'yellow', 'invert'
        """
        if self.is_inverted:
            return 'yellow'
        elif self.is_simple_inverted:
            return 'invert'
        else:
            return 'normal'

    def set_effect_mode(self, mode):
        """设置效果模式
        
        Args:
            mode: 效果模式 ('normal', 'yellow', 'invert')
        """
        try:
            # 先关闭所有效果
            was_changed = False
            
            if self.is_inverted:
                self.is_inverted = False
                was_changed = True
                if hasattr(self.main_app, 'is_inverted'):
                    self.main_app.is_inverted = False
                if hasattr(self.main_app, 'btn_invert'):
                    self.main_app.btn_invert.configure(bg="#f0f0f0")
                    
            if self.is_simple_inverted:
                self.is_simple_inverted = False
                was_changed = True
                if hasattr(self.main_app, 'is_simple_inverted'):
                    self.main_app.is_simple_inverted = False
                if hasattr(self.main_app, 'btn_simple_invert'):
                    self.main_app.btn_simple_invert.configure(bg="#f0f0f0")

            # 设置新的效果模式
            if mode == 'yellow':
                self.is_inverted = True
                was_changed = True
                if hasattr(self.main_app, 'is_inverted'):
                    self.main_app.is_inverted = True
                if hasattr(self.main_app, 'btn_invert'):
                    self.main_app.btn_invert.configure(bg="#90EE90")
                    
            elif mode == 'invert':
                self.is_simple_inverted = True
                was_changed = True
                if hasattr(self.main_app, 'is_simple_inverted'):
                    self.main_app.is_simple_inverted = True
                if hasattr(self.main_app, 'btn_simple_invert'):
                    self.main_app.btn_simple_invert.configure(bg="#90EE90")

            # 如果有变化，更新显示
            if was_changed:
                self.image_cache.clear()
                self.update_image()
                
                # 更新投影
                if (hasattr(self.main_app, 'projection_manager') and
                    self.main_app.projection_manager.second_window):
                    self.main_app.projection_manager.force_update_projection()
                    
        except Exception as e:
            print(f"设置效果模式失败: {e}")

    def clear_effects(self):
        """清除所有图片效果"""
        self.set_effect_mode('normal')

    def apply_current_effects(self, image):
        """对图片应用当前效果设置
        
        Args:
            image: PIL.Image对象
            
        Returns:
            PIL.Image: 应用效果后的图片
        """
        try:
            if not image:
                return image
                
            # 根据当前效果模式应用效果
            if self.is_inverted:
                return self.apply_yellow_text_effect(image)
            elif self.is_simple_inverted:
                return self.simple_invert_image(image)
            else:
                return image
                
        except Exception as e:
            print(f"应用当前效果失败: {e}")
            return image

    def get_effect_preview(self, image, effect_mode):
        """获取效果预览
        
        Args:
            image: PIL.Image对象
            effect_mode: 效果模式 ('normal', 'yellow', 'invert')
            
        Returns:
            PIL.Image: 效果预览图片
        """
        try:
            if not image or effect_mode == 'normal':
                return image
                
            if effect_mode == 'yellow':
                return self.apply_yellow_text_effect(image)
            elif effect_mode == 'invert':
                return self.simple_invert_image(image)
            else:
                return image
                
        except Exception as e:
            print(f"获取效果预览失败: {e}")
            return image
    
    # ==================== 图片保存功能 ====================
    
    def save_effect_image(self):
        """另存图（根据当前效果状态保存）- 性能优化版本

        Returns:
            bool: 保存是否成功
        """
        if not self.current_image:
            messagebox.showwarning("警告", "请先打开一张图片！")
            return False

        try:
            # 确定效果类型（但不立即处理图像）
            if self.is_inverted:
                effect_name = "变色"
            elif self.is_simple_inverted:
                effect_name = "反色"
            else:
                effect_name = "原图"

            # 获取当前图片的名称和路径信息
            current_image_file = None
            if hasattr(self.main_app, "current_path") and self.main_app.current_path:
                current_image_file = self.main_app.current_path

            if current_image_file:
                original_path = Path(current_image_file)
                suggested_name = original_path.name
                initial_dir = str(original_path.parent)
            else:
                suggested_name = f"{effect_name}图片.png"
                initial_dir = str(Path.home())

            # 立即弹出保存文件对话框（优化：简化文件类型选项）
            save_path = filedialog.asksaveasfilename(
                title=f"另存{effect_name}图",
                initialfile=suggested_name,
                initialdir=initial_dir,
                filetypes=[
                    ("PNG files", "*.png"),
                    ("JPEG files", "*.jpg"),
                    ("All files", "*.*"),
                ],
            )

            if save_path:
                # 只有在用户确认保存时才处理图像（延迟处理优化）
                if self.is_inverted:
                    effect_image = self.apply_yellow_text_effect(self.current_image.copy())
                elif self.is_simple_inverted:
                    effect_image = self.simple_invert_image(self.current_image.copy())
                else:
                    effect_image = self.current_image.copy()

                success = self._save_image_with_format(effect_image, save_path)
                if success:
                    # 静默保存，无提示弹窗
                    return True
                else:
                    return False
            else:
                return False  # 用户取消保存

        except Exception as e:
            print(f"保存效果图片失败: {e}")
            messagebox.showerror("错误", f"保存效果图片失败:\n{str(e)}")
            return False

    def save_current_display_image(self):
        """保存当前显示的图片（包含当前效果和缩放）
        
        Returns:
            bool: 保存是否成功
        """
        if not self.current_image:
            messagebox.showwarning("警告", "请先打开一张图片！")
            return False

        try:
            # 获取当前显示尺寸
            canvas_width, canvas_height = self.main_app.get_effective_screen_size(
                is_projection_screen=False
            )
            new_width, new_height = self._calculate_display_size(canvas_width, canvas_height)
            
            # 生成当前显示的图片
            display_image = self._resize_and_apply_effects(new_width, new_height)
            
            if not display_image:
                messagebox.showerror("错误", "生成显示图片失败")
                return False

            # 获取保存路径
            save_path = self._get_save_path("显示图片")
            
            if save_path:
                success = self._save_image_with_format(display_image, save_path)
                if success:
                    messagebox.showinfo("成功", f"显示图片已保存到:\n{save_path}")
                    return True
                else:
                    return False
            else:
                return False

        except Exception as e:
            print(f"保存显示图片失败: {e}")
            messagebox.showerror("错误", f"保存显示图片失败:\n{str(e)}")
            return False

    def save_original_size_effect_image(self):
        """保存原始尺寸的效果图片
        
        Returns:
            bool: 保存是否成功
        """
        if not self.current_image:
            messagebox.showwarning("警告", "请先打开一张图片！")
            return False

        try:
            # 应用当前效果到原始尺寸图片
            effect_image = self.apply_current_effects(self.current_image.copy())

            # 获取保存路径
            effect_mode = self.get_current_effect_mode()
            if effect_mode == 'yellow':
                filename_prefix = "黄字原图"
            elif effect_mode == 'invert':
                filename_prefix = "反色原图"
            else:
                filename_prefix = "原图"
                
            save_path = self._get_save_path(filename_prefix)
            
            if save_path:
                success = self._save_image_with_format(effect_image, save_path)
                if success:
                    messagebox.showinfo("成功", f"原始尺寸效果图片已保存到:\n{save_path}")
                    return True
                else:
                    return False
            else:
                return False

        except Exception as e:
            print(f"保存原始尺寸效果图片失败: {e}")
            messagebox.showerror("错误", f"保存原始尺寸效果图片失败:\n{str(e)}")
            return False

    def _get_save_path(self, filename_prefix):
        """获取保存路径
        
        Args:
            filename_prefix: 文件名前缀
            
        Returns:
            str: 保存路径或None
        """
        try:
            # 获取当前图片的名称和路径信息
            current_image_file = None
            if hasattr(self.main_app, "current_path") and self.main_app.current_path:
                current_image_file = self.main_app.current_path

            if current_image_file:
                original_path = Path(current_image_file)
                suggested_name = f"{filename_prefix}_{original_path.stem}{original_path.suffix}"
                initial_dir = str(original_path.parent)
            else:
                suggested_name = f"{filename_prefix}.png"
                initial_dir = str(Path.home())

            # 弹出保存文件对话框
            save_path = filedialog.asksaveasfilename(
                title=f"保存{filename_prefix}",
                initialfile=suggested_name,
                initialdir=initial_dir,
                filetypes=[
                    ("PNG files", "*.png"),
                    ("JPEG files", "*.jpg"),
                    ("BMP files", "*.bmp"),
                    ("TIFF files", "*.tiff"),
                    ("All files", "*.*"),
                ],
            )

            return save_path

        except Exception as e:
            print(f"获取保存路径失败: {e}")
            return None

    def _save_image_with_format(self, image, save_path):
        """根据文件扩展名保存图片
        
        Args:
            image: PIL.Image对象
            save_path: 保存路径
            
        Returns:
            bool: 保存是否成功
        """
        try:
            # 根据文件扩展名确定保存格式
            file_ext = Path(save_path).suffix.lower()
            
            if file_ext == ".png":
                image.save(save_path, "PNG", optimize=True)
            elif file_ext in [".jpg", ".jpeg"]:
                # JPEG不支持透明度，需要转换为RGB
                if image.mode in ("RGBA", "LA"):
                    # 创建黑色背景
                    rgb_image = Image.new("RGB", image.size, (0, 0, 0))
                    if image.mode == "RGBA":
                        rgb_image.paste(image, mask=image.split()[-1])  # 使用alpha通道作为mask
                    else:
                        rgb_image.paste(image)
                    rgb_image.save(save_path, "JPEG", quality=self._jpeg_quality, optimize=True)
                else:
                    image.save(save_path, "JPEG", quality=self._jpeg_quality, optimize=True)
            elif file_ext == ".bmp":
                # BMP不支持透明度
                if image.mode in ("RGBA", "LA"):
                    rgb_image = Image.new("RGB", image.size, (255, 255, 255))
                    if image.mode == "RGBA":
                        rgb_image.paste(image, mask=image.split()[-1])
                    else:
                        rgb_image.paste(image)
                    rgb_image.save(save_path, "BMP")
                else:
                    image.save(save_path, "BMP")
            elif file_ext in [".tiff", ".tif"]:
                image.save(save_path, "TIFF", compression="lzw")
            else:
                # 其他格式，默认使用PNG
                image.save(save_path, "PNG", optimize=True)

            return True

        except Exception as e:
            print(f"保存图片失败: {e}")
            messagebox.showerror("错误", f"保存图片失败:\n{str(e)}")
            return False


class ImageCache:
    """图片缓存管理器
    
    提供内存缓存和预览缓存的管理功能
    """
    
    def __init__(self):
        """初始化缓存"""
        self.memory_cache = LRUCache(150)  # 普通图片缓存
        self.preview_cache = LRUCache(50)  # 预览图缓存
        self._cache_stats = {
            'hits': 0,
            'misses': 0,
            'total_requests': 0
        }
        
    def clear(self):
        """清空所有缓存"""
        self.memory_cache.clear()
        self.preview_cache.clear()
        # print("图片缓存已清空")
        
    def get_image(self, path, size):
        """从内存缓存获取图片
        
        Args:
            path: 图片路径
            size: 图片尺寸 (width, height)
            
        Returns:
            PIL.Image: 缓存的图片或None
        """
        self._cache_stats['total_requests'] += 1
        
        key = f"{path}_{size}" if size else path
        
        if key in self.memory_cache:
            self._cache_stats['hits'] += 1
            return self.memory_cache[key]
        
        self._cache_stats['misses'] += 1
        return None
        
    def set_image(self, path, size, image):
        """缓存图片
        
        Args:
            path: 图片路径
            size: 图片尺寸
            image: PIL.Image对象
        """
        key = f"{path}_{size}" if size else path
        self.memory_cache[key] = image
        
    def load_and_resize(self, path, size):
        """加载并调整图片尺寸
        
        Args:
            path: 图片路径  
            size: 目标尺寸 (width, height)
            
        Returns:
            PIL.Image: 处理后的图片或None
        """
        try:
            # 检查缓存
            cached_image = self.get_image(path, size)
            if cached_image:
                return cached_image
            
            # 加载图片
            image = Image.open(path)
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # 调整尺寸
            if size and size != (image.width, image.height):
                image = image.resize(size, Image.Resampling.LANCZOS)
            
            # 缓存结果
            self.set_image(path, size, image)
            
            return image
            
        except Exception as e:
            print(f"加载图片失败 {path}: {e}")
            return None

    def __getitem__(self, key):
        """缓存项获取"""
        return self.memory_cache[key]

    def __setitem__(self, key, value):
        """缓存项设置"""
        self.memory_cache[key] = value

    def __contains__(self, key):
        """检查缓存中是否存在某个键"""
        return key in self.memory_cache

    def __len__(self):
        """返回缓存中的项目数量"""
        return len(self.memory_cache)

    def get_cache_stats(self):
        """获取缓存统计信息
        
        Returns:
            dict: 缓存统计数据
        """
        hit_rate = 0
        if self._cache_stats['total_requests'] > 0:
            hit_rate = self._cache_stats['hits'] / self._cache_stats['total_requests']
            
        return {
            'memory_cache_size': len(self.memory_cache),
            'preview_cache_size': len(self.preview_cache),
            'memory_cache_maxsize': self.memory_cache.maxsize,
            'preview_cache_maxsize': self.preview_cache.maxsize,
            'hit_rate': hit_rate,
            'total_requests': self._cache_stats['total_requests'],
            'hits': self._cache_stats['hits'],
            'misses': self._cache_stats['misses']
        }
        
    def optimize_cache(self):
        """优化缓存使用"""
        # 检查内存使用情况
        try:
            import psutil
            memory_percent = psutil.virtual_memory().percent
            
            if memory_percent > 80:  # 内存使用超过80%
                # 清理部分缓存
                old_size = len(self.memory_cache)
                self.memory_cache.clear()
                # print(f"内存使用率过高({memory_percent:.1f}%)，已清理{old_size}个缓存项")
                
        except ImportError:
            # 如果没有psutil，使用简单的缓存管理
            if len(self.memory_cache) > 100:
                self.memory_cache.clear()
                # print("缓存项过多，已清理缓存")