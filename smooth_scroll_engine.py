#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高性能平滑滚动引擎 - GPU加速版本
基于专业动画库的设计理念，提供丝滑的滚动体验
支持GPU硬件加速渲染，提供更流畅的滚动动画
"""

import time
import math
from typing import Callable, Optional
from collections import deque

# GPU加速支持
try:
    from gpu_scroll_engine import GPUScrollEngine, TkinterGPUBridge
    GPU_AVAILABLE = True
except ImportError as e:
    GPU_AVAILABLE = False

class EasingFunctions:
    """专业级缓动函数集合"""
    
    @staticmethod
    def linear(t: float) -> float:
        """线性缓动"""
        return t
    


    @staticmethod
    def optimized_ease_in_out_cubic(t: float) -> float:
        """优化的三次缓动 - 减少分支判断，性能更佳"""
        return t * t * (3.0 - 2.0 * t)
    
    @staticmethod
    def ease_out_expo(t: float) -> float:
        """指数缓出 - 快速开始，平滑结束"""
        if t >= 1.0:
            return 1.0
        elif t <= 0.0:
            return 0.0
        else:
            return 1 - pow(2, -10 * t)
    

    
    @staticmethod
    def bezier_ease(t: float, p1x: float = 0.25, p1y: float = 0.1,
                   p2x: float = 0.25, p2y: float = 1.0) -> float:
        """贝塞尔曲线缓动 - 精确的三次贝塞尔实现"""
        # 边界处理
        if t <= 0.0:
            return 0.0
        elif t >= 1.0:
            return 1.0

        # 精确的三次贝塞尔曲线实现
        # 控制点: (0,0), (p1x,p1y), (p2x,p2y), (1,1)
        u = 1 - t
        # 完整的贝塞尔公式，使用所有控制点
        return (3 * u * u * t * p1y +
                3 * u * t * t * p2y +
                t * t * t)



    @staticmethod
    def css_ease_in_out() -> float:
        """CSS ease-in-out 等价函数 - cubic-bezier(0.42, 0, 0.58, 1)"""
        def ease_func(t: float) -> float:
            return EasingFunctions.bezier_ease(t, 0.42, 0, 0.58, 1.0)
        return ease_func

class HighPerformanceScrollEngine:
    """高性能滚动引擎"""
    
    def __init__(self, canvas, projection_manager=None):
        self.canvas = canvas
        self.projection_manager = projection_manager
        
        # 动画状态
        self.is_animating = False
        self.animation_id = None
        
        # 性能优化
        self.target_fps = 120  # 更高的帧率
        self.frame_interval = 1000 / self.target_fps  # 约8.33ms
        
        # 动画参数
        self.start_position = 0.0
        self.target_position = 0.0
        self.current_position = 0.0
        self.start_time = 0.0
        self.duration = 1.0
        self.easing_function = EasingFunctions.optimized_ease_in_out_cubic
        
        # 回调函数
        self.on_update_callback: Optional[Callable] = None
        self.on_complete_callback: Optional[Callable] = None
        
        # 性能监控
        self.frame_count = 0
        self.total_frame_time = 0.0
        self.last_performance_report = 0.0
        
        # 自适应性能调整
        self.adaptive_quality = True
        self.current_fps = self.target_fps
        self.performance_samples = deque(maxlen=100)  # 使用 deque 优化性能
        
    def scroll_to(self, target_position: float, duration: float = 1.0,
                  easing: str = "optimized_cubic",
                  on_complete: Optional[Callable] = None) -> None:
        """启动平滑滚动动画"""

        # 停止当前动画
        self.stop_animation()

        # 获取当前位置
        current_view = self.canvas.yview()
        self.start_position = current_view[0]
        self.target_position = target_position
        self.current_position = self.start_position

        # 如果位置相同，直接完成
        if abs(self.target_position - self.start_position) < 0.001:
            if on_complete:
                on_complete()
            return

        # 特殊处理：如果时长为0，直接跳转
        if duration <= 0:
            self._update_canvas_position(self.target_position)
            if self.projection_manager:
                self._update_projection()
            if on_complete:
                on_complete()
            return
        
        # 设置动画参数
        self.duration = duration
        self.start_time = time.perf_counter()  # 使用高精度计时器
        self.on_complete_callback = on_complete
        
        # 设置缓动函数
        easing_map = {
            "linear": EasingFunctions.linear,

            "optimized_cubic": EasingFunctions.optimized_ease_in_out_cubic,



            "ease_out_expo": EasingFunctions.ease_out_expo,

            "bezier": lambda t: EasingFunctions.bezier_ease(t, 0.25, 0.1, 0.25, 1.0),

            "css_ease_in_out": EasingFunctions.css_ease_in_out()
        }
        self.easing_function = easing_map.get(easing, EasingFunctions.optimized_ease_in_out_cubic)
        
        # 开始动画
        self.is_animating = True
        self.frame_count = 0
        self.total_frame_time = 0.0

        # 预计算位置差值，避免重复计算
        self.position_delta = self.target_position - self.start_position

        self._animate_frame()
    
    def _animate_frame(self) -> None:
        """动画帧处理"""
        if not self.is_animating:
            return
        
        frame_start_time = time.perf_counter()
        
        # 计算动画进度
        current_time = time.perf_counter()
        elapsed = current_time - self.start_time
        progress = min(elapsed / self.duration, 1.0)
        
        # 应用缓动函数
        try:
            eased_progress = self.easing_function(progress)
        except Exception as e:
            print(f"缓动函数错误: {e}, progress={progress}")
            eased_progress = progress  # 降级到线性

        # 计算新位置（使用预计算的差值）
        self.current_position = self._calculate_position(eased_progress)

        # 可选的调试信息（注释掉以提高性能）
        # if self.frame_count % 30 == 0 or progress >= 1.0:
        #     print(f"动画帧: progress={progress:.3f}, eased={eased_progress:.3f}, pos={self.current_position:.3f}")
        
        # 更新Canvas位置
        self._update_canvas_position(self.current_position)
        
        # 更新投影（如果需要）
        if self.projection_manager:
            self._update_projection()
        
        # 调用更新回调
        if self.on_update_callback:
            self.on_update_callback(self.current_position, progress)
        
        # 检查动画是否完成
        if progress >= 1.0:
            self._complete_animation()
        else:
            # 性能监控和自适应调整
            frame_end_time = time.perf_counter()
            frame_time = (frame_end_time - frame_start_time) * 1000
            self._update_performance_stats(frame_time)
            
            # 调度下一帧
            interval = self._get_adaptive_interval()
            self.animation_id = self.canvas.after(int(interval), self._animate_frame)

    def _calculate_position(self, eased_progress: float) -> float:
        """优化位置计算，减少重复计算"""
        if abs(self.position_delta) < 0.001:
            return self.target_position

        return self.start_position + self.position_delta * eased_progress
    
    def _update_canvas_position(self, position: float) -> None:
        """更新Canvas位置（优化版）"""
        try:
            # 使用yview_moveto进行位置更新
            self.canvas.yview_moveto(position)
        except Exception as e:
            print(f"Canvas位置更新失败: {e}")
    
    def _update_projection(self) -> None:
        """更新投影（优化版）"""
        try:
            if hasattr(self.projection_manager, 'sync_projection_screen_absolute'):
                # 在动画期间同步投影
                self.projection_manager.sync_projection_screen_absolute()
        except Exception as e:
            print(f"投影更新失败: {e}")
    
    def _update_performance_stats(self, frame_time: float) -> None:
        """更新性能统计（优化版）"""
        self.frame_count += 1
        self.total_frame_time += frame_time
        # deque 自动维护最大长度，无需手动 pop
        self.performance_samples.append(frame_time)

        # 每秒报告一次性能
        current_time = time.perf_counter()
        if current_time - self.last_performance_report >= 1.0:
            self._report_performance()
            self.last_performance_report = current_time
    
    def _get_adaptive_interval(self) -> float:
        """获取自适应帧间隔（优化版）"""
        if not self.adaptive_quality or len(self.performance_samples) < 10:
            return self.frame_interval

        # 计算最近10帧的平均时间（deque 支持高效切片）
        recent_samples = list(self.performance_samples)[-10:]
        avg_frame_time = sum(recent_samples) / len(recent_samples)

        # 动态调整帧率，使用更平滑的调整策略
        target_frame_time = self.frame_interval
        if avg_frame_time > target_frame_time * 1.5:
            # 性能不足，降低帧率
            self.current_fps = max(60, self.current_fps * 0.95)
        elif avg_frame_time < target_frame_time * 0.8:
            # 性能充足，提升帧率
            self.current_fps = min(self.target_fps, self.current_fps * 1.05)

        return 1000 / self.current_fps
    
    def _report_performance(self) -> None:
        """报告性能统计（优化版）"""
        if self.frame_count == 0:
            return

        avg_frame_time = self.total_frame_time / self.frame_count
        actual_fps = 1000 / avg_frame_time if avg_frame_time > 0 else 0

        # 可选的性能日志（生产环境建议关闭）
        if hasattr(self, 'debug_performance') and self.debug_performance:
            print(f"滚动动画性能: {actual_fps:.1f} FPS, 平均帧时间: {avg_frame_time:.2f}ms")

        # 重置统计
        self.frame_count = 0
        self.total_frame_time = 0.0
    
    def _complete_animation(self) -> None:
        """完成动画"""
        self.is_animating = False
        
        # 确保到达精确位置
        self._update_canvas_position(self.target_position)
        if self.projection_manager:
            self._update_projection()
        
        # 调用完成回调
        if self.on_complete_callback:
            self.on_complete_callback()
        
        #print(f"滚动动画完成: {self.frame_count} 帧")
    
    def stop_animation(self) -> None:
        """停止当前动画"""
        if self.animation_id:
            self.canvas.after_cancel(self.animation_id)
            self.animation_id = None
        self.is_animating = False
    
    def set_update_callback(self, callback: Callable) -> None:
        """设置更新回调函数"""
        self.on_update_callback = callback
    
    def is_running(self) -> bool:
        """检查动画是否正在运行"""
        return self.is_animating
    
    def get_current_position(self) -> float:
        """获取当前位置"""
        return self.current_position

    def set_debug_performance(self, enabled: bool) -> None:
        """设置性能调试模式"""
        self.debug_performance = enabled

    def get_performance_stats(self) -> dict:
        """获取性能统计信息"""
        if len(self.performance_samples) == 0:
            return {"avg_frame_time": 0, "current_fps": self.current_fps, "samples": 0}

        recent_samples = list(self.performance_samples)[-10:]
        avg_frame_time = sum(recent_samples) / len(recent_samples)

        return {
            "avg_frame_time": avg_frame_time,
            "current_fps": self.current_fps,
            "target_fps": self.target_fps,
            "samples": len(self.performance_samples),
            "adaptive_quality": self.adaptive_quality
        }

    def scroll_by(self, delta_y: float, duration: float = 1.0,
                  easing: str = "optimized_cubic") -> None:
        """相对滚动 - 从当前位置滚动指定距离"""
        current_view = self.canvas.yview()
        current_position = current_view[0]
        target_position = current_position + delta_y

        # 确保目标位置在有效范围内
        target_position = max(0.0, min(1.0, target_position))

        self.scroll_to(target_position, duration, easing)

    def scroll_to_element(self, element_id: str, duration: float = 1.0,
                         easing: str = "optimized_cubic", offset: float = 0.0) -> None:
        """滚动到指定元素（如果支持元素查找）"""
        # 这里可以根据具体的 Canvas 实现来查找元素位置
        # 示例实现，实际使用时需要根据具体情况调整
        try:
            # 假设有方法可以获取元素位置
            if hasattr(self.canvas, 'find_element_position'):
                element_position = self.canvas.find_element_position(element_id)
                target_position = element_position + offset
                self.scroll_to(target_position, duration, easing)
            else:
                print(f"警告: 无法找到元素 {element_id}，Canvas 不支持元素查找")
        except Exception as e:
            print(f"滚动到元素失败: {e}")

    def get_easing_functions(self) -> list:
        """获取所有可用的缓动函数名称"""
        return [
            "linear", "optimized_cubic",
            "ease_out_expo",
            "bezier", "css_ease_in_out"
        ]


# 插件系统基础类
class ScrollPlugin:
    """滚动插件基础类"""

    def __init__(self, scroll_engine):
        self.scroll_engine = scroll_engine
        self.enabled = True

    def on_scroll_start(self, start_pos: float, target_pos: float) -> None:
        """滚动开始时调用"""
        pass

    def on_scroll_update(self, current_pos: float, progress: float) -> None:
        """滚动更新时调用"""
        pass

    def on_scroll_complete(self) -> None:
        """滚动完成时调用"""
        pass

    def transform_easing(self, progress: float) -> float:
        """变换缓动进度"""
        return progress


# 示例插件：弹性滚动
class ElasticScrollPlugin(ScrollPlugin):
    """弹性滚动插件 - 添加轻微的弹性效果"""

    def __init__(self, scroll_engine, elasticity: float = 0.1):
        super().__init__(scroll_engine)
        self.elasticity = elasticity
        self.overshoot_amount = 0.0

    def transform_easing(self, progress: float) -> float:
        """添加弹性效果"""
        if progress >= 1.0:
            return 1.0

        # 添加轻微的超调效果
        elastic_progress = progress + self.elasticity * math.sin(progress * math.pi)
        return min(1.0, elastic_progress)


# 示例插件：滚动音效
class SoundEffectPlugin(ScrollPlugin):
    """滚动音效插件"""

    def __init__(self, scroll_engine, enable_sound: bool = True):
        super().__init__(scroll_engine)
        self.enable_sound = enable_sound

    def on_scroll_start(self, start_pos: float, target_pos: float) -> None:
        """播放滚动开始音效"""
        if self.enable_sound:
            # 这里可以集成音效播放库
            print("🔊 滚动开始音效")

    def on_scroll_complete(self) -> None:
        """播放滚动完成音效"""
        if self.enable_sound:
            print("🔊 滚动完成音效")


# 工厂函数：创建优化的滚动引擎
def create_optimized_scroll_engine(canvas, projection_manager=None,
                                 target_fps: int = 120,
                                 default_easing: str = "optimized_cubic",
                                 enable_debug: bool = False) -> HighPerformanceScrollEngine:
    """
    创建优化配置的滚动引擎

    Args:
        canvas: Canvas 对象
        projection_manager: 投影管理器（可选）
        target_fps: 目标帧率
        default_easing: 默认缓动函数
        enable_debug: 是否启用调试模式

    Returns:
        配置好的滚动引擎实例
    """
    engine = HighPerformanceScrollEngine(canvas, projection_manager)
    engine.target_fps = target_fps
    engine.frame_interval = 1000 / target_fps
    engine.easing_function = getattr(EasingFunctions, default_easing, EasingFunctions.optimized_ease_in_out_cubic)

    if enable_debug:
        engine.set_debug_performance(True)

    return engine


# 使用示例
"""
# 基本使用
canvas = your_canvas_object
engine = create_optimized_scroll_engine(canvas, target_fps=120, enable_debug=True)

# 使用新的缓动函数
engine.scroll_to(0.5, duration=1.0, easing="optimized_cubic")  # 使用优化的三次缓动
engine.scroll_to(0.8, duration=0.8, easing="optimized_cubic")  # 使用优化的三次缓动

# 相对滚动
engine.scroll_by(0.1, duration=0.5)  # 向下滚动 10%

# 获取性能统计
stats = engine.get_performance_stats()
print(f"平均帧时间: {stats['avg_frame_time']:.2f}ms")
print(f"当前帧率: {stats['current_fps']:.1f} FPS")

# 查看可用的缓动函数
print("可用缓动函数:", engine.get_easing_functions())
"""


# GPU加速滚动引擎 - 最高性能版本
class GPUAcceleratedScrollEngine(HighPerformanceScrollEngine):
    """GPU加速的滚动引擎 - 提供最佳性能和流畅度"""
    
    def __init__(self, canvas, projection_manager=None):
        super().__init__(canvas, projection_manager)
        
        # 尝试初始化GPU加速
        self.gpu_bridge = None
        self.use_gpu = False
        
        if GPU_AVAILABLE:
            try:
                self.gpu_bridge = TkinterGPUBridge(canvas, projection_manager)
                self.use_gpu = True
                
                # GPU模式下的优化设置
                self.target_fps = 120
                self.frame_interval = 1000.0 / 120.0
                self.adaptive_quality = False
                
            except Exception as e:
                self.use_gpu = False
                self.gpu_bridge = None
        
        if not self.use_gpu:
            # CPU模式 - VSync同步优化
            self.target_fps = 120
            self.frame_interval = 1000.0 / 120.0
            self.current_fps = 120
            self.adaptive_quality = False
            self.position_threshold = 0.0001
            self.last_update_position = 0.0
    
    def load_image(self, image_path):
        """加载图片到GPU（如果可用）"""
        if self.use_gpu and self.gpu_bridge:
            return self.gpu_bridge.load_image(image_path)
        return True  # CPU模式下总是返回True
    
    def scroll_to(self, target_position: float, duration: float = 1.0,
                  easing: str = "optimized_cubic",
                  on_complete: Optional[Callable] = None) -> None:
        """启动平滑滚动动画 - GPU辅助Canvas滚动"""
        
        if self.use_gpu and self.gpu_bridge:
            # GPU辅助的Canvas滚动模式
            try:
                # 停止当前动画
                self.stop_animation()
                
                # 设置回调
                self.on_complete_callback = on_complete
                
                # 启动GPU辅助的Canvas滚动
                self._start_gpu_assisted_scroll(target_position, duration, easing)
                
                return
                
            except Exception as e:
                # 降级到CPU模式
                self.use_gpu = False
        
        # CPU模式滚动（VSync优化版本）
        self._cpu_scroll_to(target_position, duration, easing, on_complete)
    
    def _start_gpu_assisted_scroll(self, target_position: float, duration: float, easing: str):
        """启动GPU辅助的Canvas滚动动画"""
        # 获取当前位置
        current_view = self.canvas.yview()
        self.start_position = current_view[0]
        self.target_position = target_position
        self.current_position = self.start_position
        
        # 如果位置相同，直接完成
        if abs(self.target_position - self.start_position) < 0.001:
            if self.on_complete_callback:
                self.on_complete_callback()
            return
        
        # 设置动画参数
        self.duration = duration
        self.start_time = time.perf_counter()
        
        # GPU加载当前图片（从主应用程序获取）
        current_image_path = None
        
        # 方法1: 从projection_manager获取主应用程序
        if self.projection_manager and hasattr(self.projection_manager, 'main_app'):
            main_app = self.projection_manager.main_app
            current_image_path = getattr(main_app, 'current_path', None)
        
        # 方法2: 如果没有projection_manager，尝试从canvas层级获取
        if not current_image_path:
            # 尝试通过canvas找到主应用程序
            master = self.canvas.master
            while master and not current_image_path:
                # 检查当前层级是否有current_path
                current_image_path = getattr(master, 'current_path', None)
                if not current_image_path:
                    # 检查是否是主应用程序（ImageProjector实例）
                    if hasattr(master, '__class__') and 'ImageProjector' in str(master.__class__):
                        current_image_path = getattr(master, 'current_path', None)
                        break
                master = getattr(master, 'master', None)
        
        if current_image_path and self.gpu_bridge:
            self.gpu_bridge.load_image(current_image_path)
        elif self.gpu_bridge:
            # 添加调试信息帮助诊断问题
            debug_info = []
            if self.projection_manager:
                debug_info.append(f"projection_manager存在")
                if hasattr(self.projection_manager, 'main_app'):
                    debug_info.append(f"main_app存在: {type(self.projection_manager.main_app).__name__}")
                    if hasattr(self.projection_manager.main_app, 'current_path'):
                        debug_info.append(f"current_path属性存在，值为: {getattr(self.projection_manager.main_app, 'current_path', 'None')}")
                    else:
                        debug_info.append("current_path属性不存在")
                else:
                    debug_info.append("main_app不存在")
            else:
                debug_info.append("projection_manager不存在")
            
            pass  # 未找到当前图片路径，跳过GPU图片加载
        
        # 设置缓动函数
        easing_map = {
            "linear": EasingFunctions.linear,
            "optimized_cubic": EasingFunctions.optimized_ease_in_out_cubic,
            "ease_out_expo": EasingFunctions.ease_out_expo,
            "bezier": lambda t: EasingFunctions.bezier_ease(t, 0.25, 0.1, 0.25, 1.0),
            "css_ease_in_out": EasingFunctions.css_ease_in_out()
        }
        self.easing_function = easing_map.get(easing, EasingFunctions.optimized_ease_in_out_cubic)
        
        # 预计算位置差值
        self.position_delta = self.target_position - self.start_position
        
        # 开始动画
        self.is_animating = True
        self.frame_count = 0
        
        # 启动GPU辅助的动画循环
        self._gpu_assisted_animate_frame()
    
    def _gpu_assisted_animate_frame(self):
        """
GPU辅助的动画帧处理"""
        if not self.is_animating:
            return
        
        # 计算动画进度
        current_time = time.perf_counter()
        elapsed = current_time - self.start_time
        progress = min(elapsed / self.duration, 1.0)
        
        # 应用缓动函数
        try:
            eased_progress = self.easing_function(progress)
        except Exception as e:
            print(f"缓动函数错误: {e}")
            eased_progress = progress
        
        # 计算新位置
        self.current_position = self.start_position + self.position_delta * eased_progress
        
        # 更新Canvas位置（主要显示）
        self._update_canvas_position(self.current_position)
        
        # 同步GPU渲染（如果可用）
        if self.gpu_bridge and hasattr(self.gpu_bridge.gpu_engine, 'current_scroll_pos'):
            self.gpu_bridge.gpu_engine.current_scroll_pos = eased_progress
        
        # 更新投影
        if self.projection_manager:
            self._update_projection()
        
        # 检查动画是否完成
        if progress >= 1.0:
            self._complete_gpu_assisted_animation()
        else:
            # 调度下一帧（约120fps）
            self.animation_id = self.canvas.after(8, self._gpu_assisted_animate_frame)
    
    def _complete_gpu_assisted_animation(self):
        """完成GPU辅助动画"""
        self.is_animating = False
        
        # 确保到达精确位置
        self._update_canvas_position(self.target_position)
        if self.projection_manager:
            self._update_projection()
        
        # 调用完成回调
        if self.on_complete_callback:
            self.on_complete_callback()
    
    def _monitor_gpu_scroll(self):
        """监控GPU滚动完成状态（保留兼容）"""
        if (self.gpu_bridge and 
            hasattr(self.gpu_bridge.gpu_engine, 'is_animating') and 
            self.gpu_bridge.gpu_engine.is_animating):
            
            # 继续监控
            self.canvas.after(16, self._monitor_gpu_scroll)
        else:
            # GPU滚动完成
            if self.on_complete_callback:
                self.on_complete_callback()
    
    def _cpu_scroll_to(self, target_position: float, duration: float = 1.0,
                      easing: str = "optimized_cubic",
                      on_complete: Optional[Callable] = None) -> None:
        """CPU模式滚动 - VSync优化版本"""
        # 使用父类的scroll_to方法
        super().scroll_to(target_position, duration, easing, on_complete)
    
    def get_performance_info(self):
        """获取性能信息"""
        base_info = super().get_performance_stats()
        
        if self.use_gpu and self.gpu_bridge:
            gpu_info = self.gpu_bridge.get_performance_info()
            return {
                **base_info,
                'gpu_enabled': True,
                'gpu_vendor': gpu_info.get('gpu_vendor', 'Unknown'),
                'gpu_renderer': gpu_info.get('gpu_renderer', 'Unknown'),
                'gpu_fps': gpu_info.get('fps', 0),
                'texture_cache_size': gpu_info.get('texture_cache_size', 0)
            }
        else:
            return {
                **base_info,
                'gpu_enabled': False,
                'mode': 'CPU (VSync优化)'
            }
    
    def cleanup(self):
        """清理资源"""
        if self.gpu_bridge:
            self.gpu_bridge.cleanup()
            self.gpu_bridge = None
        
        self.stop_animation()


# VSync同步优化类 - 解决120Hz显示器闪烁问题 (保留向后兼容)
class VsyncOptimizedScrollEngine(GPUAcceleratedScrollEngine):
    """VSync同步优化的滚动引擎 - 现在基于GPU加速引擎"""
    
    def __init__(self, canvas, projection_manager=None):
        super().__init__(canvas, projection_manager)
    
    def _get_adaptive_interval(self) -> float:
        """固定帧间隔，与120Hz显示器同步"""
        return self.frame_interval
    
    def _update_canvas_position(self, position: float) -> None:
        """优化的Canvas位置更新 - 减少闪烁"""
        try:
            # 只有位置变化足够大时才更新
            if hasattr(self, 'last_update_position'):
                if abs(position - self.last_update_position) > getattr(self, 'position_threshold', 0.0001):
                    self.canvas.yview_moveto(position)
                    self.last_update_position = position
            else:
                self.canvas.yview_moveto(position)
                self.last_update_position = position
        except Exception as e:
            print(f"Canvas位置更新失败: {e}")
