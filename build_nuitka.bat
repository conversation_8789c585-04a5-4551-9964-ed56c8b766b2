@echo off
echo 开始使用 Nuitka 编译 Canvas Cast V2.0...

python -m nuitka ^
  --standalone ^
  --enable-plugin=tk-inter ^
  --enable-plugin=numpy ^
  --enable-plugin=multiprocessing ^
  --windows-icon-from-ico=baodian.ico ^
  --include-data-files=baodian.ico=baodian.ico ^
  --include-data-files=config.json=config.json ^
  --include-data-files=pyimages.db=pyimages.db ^
  --include-package-data=pypinyin ^
  --include-module=pynput ^
  --include-module=screeninfo ^
  --include-module=cachetools ^
  --include-package=OpenGL_accelerate ^
  --include-module=PIL ^
  --include-module=sqlite3 ^
  --include-module=moderngl ^
  --include-module=glcontext ^
  --include-module=psutil ^
  --include-module=pygame ^
  --follow-imports ^
  --show-progress ^
  --disable-console ^
  --output-dir=build ^
  --windows-company-name="Canvas Cast" ^
  --windows-product-name="Canvas Cast V2.2" ^
  --windows-file-version="*******" ^
  --windows-product-version="2.2.0" ^
  --windows-file-description="Canvas Cast" ^
  main.py

echo.
echo 编译完成！
echo 可执行文件位于: build\main.dist\main.exe
pause
