"""
事件处理模块
负责处理鼠标滚轮事件、键盘事件、窗口事件、拖拽事件等
"""

import tkinter as tk


class EventHandler:
    """事件处理器"""
    
    def __init__(self, app_instance):
        """初始化事件处理器
        
        Args:
            app_instance: 主应用实例的引用
        """
        self.app = app_instance
        
        # 初始化拖拽数据
        self.drag_data = {"item": None, "x": 0, "y": 0}
    
    def on_mousewheel(self, event):
        """鼠标滚轮事件处理（修复版本）"""
        if self.app.image:
            # 计算滚动量
            delta = -1 * (event.delta // 120)
            # 主屏幕滚动
            self.app.canvas.yview_scroll(delta, "units")

            # 投影需要实时同步，不能使用防抖
            if self.app.second_canvas and self.app.sync_enabled:
                self.app.sync_projection_screen_absolute()
                self.app.second_window.update()

            # 预览线可以使用防抖机制
            self.app._schedule_ui_update('preview_lines')

    def handle_arrow_key(self, direction):
        """处理方向键事件"""
        # 检查是否在原图模式
        if self.app.original_mode and hasattr(self.app, "yuantu_manager"):
            # 原图模式下：切换相似图片
            if direction == "prev":
                self.app.yuantu_manager.find_and_switch_similar_image(direction="prev")
            elif direction == "next":
                self.app.yuantu_manager.find_and_switch_similar_image(direction="next")
        else:
            # 正常模式下：跳转关键帧
            if direction == "prev":
                self.app.step_to_prev_keyframe()
            elif direction == "next":
                self.app.step_to_next_keyframe()

    def handle_pageup_key(self):
        """处理PageUp键事件"""
        # 检查是否在暂停状态下
        if (self.app.is_auto_playing and self.app.auto_player and
            hasattr(self.app.auto_player, 'is_paused') and self.app.auto_player.is_paused):
            # 暂停状态下：减少时间并直接跳转到上一帧
            print("暂停状态下按PageUp：减少时间并跳转到上一帧")
            if hasattr(self.app.auto_player, 'handle_pageup_in_pause'):
                self.app.auto_player.handle_pageup_in_pause()
            return

        # 检查是否在原图模式
        if self.app.original_mode and hasattr(self.app, "yuantu_manager"):
            # 原图模式下：切换到上一张相似图片
            self.app.yuantu_manager.find_and_switch_similar_image(direction="prev")
        else:
            # 正常模式下：跳转到上一个关键帧
            self.app.step_to_prev_keyframe()

    def handle_pagedown_key(self):
        """处理PageDown键事件"""
        # 检查是否在暂停状态下
        if (self.app.is_auto_playing and self.app.auto_player and
            hasattr(self.app.auto_player, 'is_paused') and self.app.auto_player.is_paused):
            # 暂停状态下：增加时间并直接跳转到下一帧
            print("暂停状态下按PageDown：增加时间并跳转到下一帧")

            # 检查是否为原图模式
            if self.app.original_mode and hasattr(self.app.auto_player, 'resume_original_mode_auto_play'):
                # 原图模式恢复播放
                if self.app.auto_player.resume_original_mode_auto_play():
                    self.app.btn_pause_resume.config(text="暂停", bg="#f0f0f0")  # 恢复普通颜色
                    print("PageDown键：恢复原图模式播放并跳转到下一帧")
            elif hasattr(self.app.auto_player, 'resume_auto_play'):
                # 普通模式恢复播放
                if self.app.auto_player.resume_auto_play():
                    self.app.btn_pause_resume.config(text="暂停", bg="#f0f0f0")  # 恢复普通颜色
                    print("PageDown键：恢复播放并跳转到下一帧")
            return

        # 检查是否在原图模式
        if self.app.original_mode and hasattr(self.app, "yuantu_manager"):
            # 原图模式下：切换到下一张相似图片
            self.app.yuantu_manager.find_and_switch_similar_image(direction="next")
        else:
            # 正常模式下：跳转到下一个关键帧（播放时减少时间）
            self.app.step_to_next_keyframe()

    def handle_escape_key(self):
        """处理ESC按键 - 结束投影"""
        if self.app.second_window:
            # 关闭投影窗口
            self.app.second_window.destroy()
            self.app.second_window = None
            self.app.second_canvas = None
            self.app.btn_show.configure(bg="#f0f0f0", text="投影")  # 恢复原色和原文本
            self.app.sync_enabled = False

            # ESC键关闭投影时也要清理全局热键
            self.app.cleanup_global_hotkeys()
            self.app.global_hotkeys_enabled = False
            # print("ESC键：投影已结束，全局热键已禁用")

    def on_tree_press(self, event):
        """处理树形控件的鼠标按下事件（用于拖放操作）"""
        # 获取点击的项目
        item = self.app.project_tree.identify_row(event.y)
        if item:
            # 记录拖动开始的项目和位置
            self.drag_data["item"] = item
            self.drag_data["x"] = event.x
            self.drag_data["y"] = event.y

    def on_tree_motion(self, event):
        """处理树形控件的鼠标移动事件（用于拖放操作）"""
        # 检查是否在拖动状态
        if self.drag_data["item"]:
            # 计算移动距离
            dx = abs(event.x - self.drag_data["x"])
            dy = abs(event.y - self.drag_data["y"])
            
            # 如果移动距离足够大，开始拖放操作
            if dx > 5 or dy > 5:
                # 这里可以添加拖放视觉反馈
                pass

    def on_tree_release(self, event):
        """处理树形控件的鼠标释放事件（用于拖放操作）"""
        if self.drag_data["item"]:
            # 获取释放位置的项目
            target_item = self.app.project_tree.identify_row(event.y)
            
            # 如果有目标项目且不是自己，执行拖放操作
            if target_item and target_item != self.drag_data["item"]:
                self._handle_drag_drop(self.drag_data["item"], target_item)
            
            # 清除拖动数据
            self.drag_data = {"item": None, "x": 0, "y": 0}

    def _handle_drag_drop(self, source_item, target_item):
        """处理拖放操作"""
        try:
            # 判断源和目标的类型
            source_is_folder = source_item.startswith("folder_")
            target_is_folder = target_item.startswith("folder_")
            
            # 只允许特定的拖放操作
            if source_is_folder and target_is_folder:
                # 文件夹到文件夹：重新排序
                self._reorder_folders(source_item, target_item)
            elif not source_is_folder and target_is_folder:
                # 图片到文件夹：移动图片到文件夹
                self._move_image_to_folder(source_item, target_item)
            elif not source_is_folder and not target_is_folder:
                # 图片到图片：重新排序图片
                self._reorder_images(source_item, target_item)
            
        except Exception as e:
            print(f"拖放操作失败: {e}")

    def _reorder_folders(self, source_folder, target_folder):
        """重新排序文件夹"""
        # 这里可以实现文件夹重排序逻辑
        print(f"重新排序文件夹: {source_folder} -> {target_folder}")

    def _move_image_to_folder(self, image_item, folder_item):
        """将图片移动到文件夹"""
        # 这里可以实现图片移动逻辑
        print(f"移动图片到文件夹: {image_item} -> {folder_item}")

    def _reorder_images(self, source_image, target_image):
        """重新排序图片"""
        # 这里可以实现图片重排序逻辑
        print(f"重新排序图片: {source_image} -> {target_image}")

    def on_window_resize(self, event):
        """处理窗口大小改变事件"""
        # 更新图片显示
        self.app.update_image()

        # 更新关键帧指示器
        if hasattr(self.app, "indicator_frame"):
            self.app.update_keyframe_indicators()

    def setup_event_bindings(self):
        """设置事件绑定"""
        # 绑定窗口大小改变事件
        self.app.root.bind("<Configure>", self.on_window_resize)

        # 保留左右方向键绑定用于在原图模式下切换图片（窗口内使用）
        self.app.root.bind("<Left>", lambda e: self.handle_arrow_key("prev"))  # 左键
        self.app.root.bind("<Right>", lambda e: self.handle_arrow_key("next"))  # 右键

        # 绑定鼠标滚轮事件
        if hasattr(self.app, 'canvas'):
            self.app.canvas.bind("<MouseWheel>", self.on_mousewheel)

        # 绑定树形控件的拖放事件
        if hasattr(self.app, 'project_tree'):
            self.app.project_tree.bind("<Button-1>", self.on_tree_press)
            self.app.project_tree.bind("<B1-Motion>", self.on_tree_motion)
            self.app.project_tree.bind("<ButtonRelease-1>", self.on_tree_release)
