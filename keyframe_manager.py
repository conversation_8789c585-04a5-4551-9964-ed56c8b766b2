"""
关键帧管理模块
负责关键帧的添加、删除、导航、缓存管理等功能
"""

import sqlite3
import time
from keyframe_navigation import KeyframeNavigation


class KeyframeManager:
    """关键帧管理器"""
    
    def __init__(self, app_instance):
        """初始化关键帧管理器
        
        Args:
            app_instance: 主应用实例的引用
        """
        self.app = app_instance
        
        # 初始化关键帧相关状态变量
        self.current_keyframe_index = -1
        self.keyframe_markers = []
        self.is_loop_enabled = True  # 默认开启循环模式
        
        # 初始化滚动速度设置
        self.scroll_duration = 0.0  # 默认滚动时间为0秒（立即跳转）
        
        # 操作历史记录（用于智能跳转判断）
        self.keyframe_operation_history = []
        self.last_operation_time = 0
        self.auto_jump_delay = 2.0  # 2秒无操作后自动跳转
        
        # 添加关键帧缓存以减少数据库查询
        self._keyframes_cache = {}
        self._cache_timestamp = {}
        self._cache_ttl = 5.0  # 缓存5秒
        
        # 添加UI更新防抖机制
        self._ui_update_pending = False
        self._last_ui_update = 0
        self._ui_update_delay = 0.02  # 20ms防抖延迟，减少延迟
        
        # 添加投影更新防抖机制
        self._projection_update_pending = False
        self._last_projection_update = 0
        self._projection_update_delay = 0.016  # 16ms防抖延迟，约60FPS

        # 初始化导航功能
        self.navigation = KeyframeNavigation(self)
    
    def add_keyframe(self, image_id, position):
        """添加关键帧 - 仅标记位置,不执行任何操作"""
        try:
            # 直接获取当前可见区域顶部的y坐标
            current_y = self.app.canvas.canvasy(0)

            with sqlite3.connect(self.app.db_path) as conn:
                # 检查是否已存在相近位置的关键帧
                cursor = conn.execute(
                    """
                    SELECT y_position
                    FROM keyframes
                    WHERE image_id = ? AND ABS(y_position - ?) < 50
                """,
                    (image_id, current_y),
                )

                if cursor.fetchone():
                    print("该位置附近已存在关键帧")
                    return False

                # 获取当前最大order_index
                cursor = conn.execute(
                    """
                    SELECT COALESCE(MAX(order_index), -1)
                    FROM keyframes
                    WHERE image_id = ?
                """,
                    (image_id,),
                )
                max_order = cursor.fetchone()[0]
                next_order = max_order + 1

                # 插入新关键帧
                conn.execute(
                    """
                    INSERT INTO keyframes (image_id, position, y_position, order_index)
                    VALUES (?, ?, ?, ?)
                """,
                    (image_id, position, int(current_y), next_order),
                )

                conn.commit()

                # 清除缓存
                self._clear_keyframes_cache(image_id)

                # 仅更新预览线显示,不执行滚动
                self.update_preview_lines()
                return True

        except Exception as e:
            print(f"添加关键帧失败: {e}")
            return False
    
    def get_keyframes(self, image_id):
        """获取指定图片的所有关键帧（带缓存优化）"""
        # 检查缓存
        current_time = time.time()
        cache_key = image_id

        if (cache_key in self._keyframes_cache and
            cache_key in self._cache_timestamp and
            current_time - self._cache_timestamp[cache_key] < self._cache_ttl):
            return self._keyframes_cache[cache_key]

        try:
            with sqlite3.connect(self.app.db_path) as conn:
                cursor = conn.execute(
                    """
                    SELECT id, position, order_index
                    FROM keyframes
                    WHERE image_id = ?
                    ORDER BY order_index
                """,
                    (image_id,),
                )
                result = cursor.fetchall()

                # 更新缓存
                self._keyframes_cache[cache_key] = result
                self._cache_timestamp[cache_key] = current_time

                return result
        except Exception as e:
            print(f"获取关键帧失败: {e}")
            return []
    
    def _clear_keyframes_cache(self, image_id=None):
        """清除关键帧缓存"""
        if image_id is None:
            # 清除所有缓存
            self._keyframes_cache.clear()
            self._cache_timestamp.clear()
        else:
            # 清除特定图片的缓存
            if image_id in self._keyframes_cache:
                del self._keyframes_cache[image_id]
            if image_id in self._cache_timestamp:
                del self._cache_timestamp[image_id]
    
    def delete_keyframe(self, keyframe_id):
        """删除单个关键帧"""
        try:
            with sqlite3.connect(self.app.db_path) as conn:
                # 获取关键帧信息用于重排序
                cursor = conn.execute(
                    """
                    SELECT image_id, order_index
                    FROM keyframes
                    WHERE id = ?
                """,
                    (keyframe_id,),
                )
                result = cursor.fetchone()
                if not result:
                    return False

                image_id, order_index = result

                # 删除关键帧
                conn.execute("DELETE FROM keyframes WHERE id = ?", (keyframe_id,))

                # 更新后续关键帧的顺序
                conn.execute(
                    """
                    UPDATE keyframes
                    SET order_index = order_index - 1
                    WHERE image_id = ? AND order_index > ?
                """,
                    (image_id, order_index),
                )

                conn.commit()

                # 清除缓存
                self._clear_keyframes_cache(image_id)

                print(f"删除关键帧成功: id={keyframe_id}")
                return True
        except Exception as e:
            print(f"删除关键帧失败: {e}")
            return False
    
    def clear_keyframes(self, image_id):
        """清除指定图片的所有关键帧"""
        try:
            with sqlite3.connect(self.app.db_path) as conn:
                conn.execute("DELETE FROM keyframes WHERE image_id = ?", (image_id,))
                conn.commit()

            # 清除缓存
            self._clear_keyframes_cache(image_id)
            return True
        except Exception as e:
            print(f"清除关键帧失败: {e}")
            return False
    
    def clear_all_keyframes(self):
        """清除所有关键帧(谨慎使用)"""
        try:
            with sqlite3.connect(self.app.db_path) as conn:
                conn.execute("DELETE FROM keyframes")
                conn.commit()
            return True
        except Exception as e:
            print(f"清除所有关键帧失败: {e}")
            return False
    
    def update_keyframe_order(self, image_id):
        """重新整理关键帧顺序"""
        try:
            with sqlite3.connect(self.app.db_path) as conn:
                # 获取所有关键帧并按position排序
                cursor = conn.execute(
                    """
                    SELECT id
                    FROM keyframes
                    WHERE image_id = ?
                    ORDER BY position
                """,
                    (image_id,),
                )

                # 更新order_index
                for index, (keyframe_id,) in enumerate(cursor.fetchall()):
                    conn.execute(
                        """
                        UPDATE keyframes
                        SET order_index = ?
                        WHERE id = ?
                    """,
                        (index, keyframe_id),
                    )

                conn.commit()
                print(f"更新关键帧顺序成功: image_id={image_id}")
                return True
        except Exception as e:
            print(f"更新关键帧顺序失败: {e}")
            return False

    def add_current_keyframe(self):
        """添加当前位置为关键帧"""
        if not hasattr(self.app, "current_image_id"):
            from tkinter import messagebox
            messagebox.showinfo("提示", "请先选择一张图片")
            return

        # 获取当前滚动位置
        position = self.app.canvas.yview()[0]

        # 添加关键帧
        if self.add_keyframe(self.app.current_image_id, position):
            # 更新指示器
            self.update_keyframe_indicators()
        else:
            from tkinter import messagebox
            messagebox.showerror("错误", "添加关键帧失败")

    def update_keyframe_indicators(self):
        """更新关键帧指示器 - 代理到UI组件（带防抖）"""
        self._schedule_ui_update('indicators')

    def update_preview_lines(self):
        """更新预览线 - 代理到UI组件（带防抖）"""
        self._schedule_ui_update('preview_lines')

    def _schedule_ui_update(self, update_type):
        """安排UI更新（防抖机制）"""
        current_time = time.time()

        # 如果距离上次更新时间太短，延迟执行
        if current_time - self._last_ui_update < self._ui_update_delay:
            if not self._ui_update_pending:
                self._ui_update_pending = True
                delay_ms = int(self._ui_update_delay * 1000)
                self.app.root.after(delay_ms, lambda: self._execute_ui_update(update_type))
        else:
            self._execute_ui_update(update_type)

    def _execute_ui_update(self, update_type):
        """执行实际的UI更新"""
        self._last_ui_update = time.time()
        self._ui_update_pending = False

        if update_type == 'indicators':
            self.app.ui_components.update_keyframe_indicators()
        elif update_type == 'preview_lines':
            self.app.ui_components.update_preview_lines()
        elif update_type == 'both':
            self.app.ui_components.update_keyframe_indicators()
            self.app.ui_components.update_preview_lines()

    def _schedule_projection_update(self):
        """安排投影更新（防抖机制）"""
        current_time = time.time()

        # 如果距离上次更新时间太短，延迟执行
        if current_time - self._last_projection_update < self._projection_update_delay:
            if not self._projection_update_pending:
                self._projection_update_pending = True
                delay_ms = int(self._projection_update_delay * 1000)
                self.app.root.after(delay_ms, self._execute_projection_update)
        else:
            self._execute_projection_update()

    def _execute_projection_update(self):
        """执行实际的投影更新"""
        self._last_projection_update = time.time()
        self._projection_update_pending = False

        if self.app.second_window and self.app.sync_enabled:
            self.app.update_projection()

    def smooth_scroll_to(self, target_position):
        """使用GPU加速的平滑滚动实现更丝滑的效果"""
        current_position = self.app.canvas.yview()[0]
        if abs(current_position - target_position) < 0.001:
            return

        # 传递全局easing
        if hasattr(self.app, "scroll_engine"):
            # 如果有GPU支持，先加载当前图片到GPU
            if (hasattr(self.app.scroll_engine, 'use_gpu') and
                self.app.scroll_engine.use_gpu and
                hasattr(self.app, 'current_path')):
                self.app.scroll_engine.load_image(self.app.current_path)

            # 执行滚动动画
            self.app.scroll_engine.scroll_to(
                target_position,
                duration=self.scroll_duration if hasattr(self, "scroll_duration") else 1.0,
                easing=getattr(self.app, 'current_easing', 'optimized_cubic')
            )
        else:
            # 降级到直接跳转
            self.app.canvas.yview_moveto(target_position)

    # ease_in_out_quart 方法已移除，使用 smooth_scroll_engine.py 中的 EasingFunctions

    # 关键帧导航方法代理
    def step_to_prev_keyframe(self):
        """执行前一个关键帧"""
        return self.navigation.step_to_prev_keyframe()

    def step_to_next_keyframe(self):
        """执行下一个关键帧"""
        return self.navigation.step_to_next_keyframe()
