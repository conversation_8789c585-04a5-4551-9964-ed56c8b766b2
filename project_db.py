"""
项目管理数据库操作模块
负责文件夹导入、同步等项目管理相关的数据库操作
使用直接的sqlite3.connect方式保持高效性能
"""

import sqlite3
from pathlib import Path
from typing import List, Tuple, Dict, Optional, Any
from concurrent.futures import ThreadPoolExecutor, as_completed


class ProjectDatabaseOperations:
    """项目管理数据库操作器"""
    
    def __init__(self, db_path: str):
        """
        初始化项目数据库操作器
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
    
    def import_folder_to_db(self, folder_path: str, get_sort_key_func) -> Tuple[int, List[str], List[str]]:
        """
        将文件夹导入数据库
        
        Args:
            folder_path: 文件夹路径
            get_sort_key_func: 排序键函数
            
        Returns:
            Tuple[folder_id, new_images, existing_images]
        """
        try:
            folder_path_obj = Path(folder_path)
            folder_name = folder_path_obj.name
            
            # 获取所有支持的图片文件
            image_files = []
            extensions = (".jpg", ".jpeg", ".png", ".bmp", ".gi", ".ti")
            
            # 递归扫描所有子文件夹
            for file_path in folder_path_obj.rglob("*"):
                if file_path.is_file() and file_path.suffix.lower() in extensions:
                    image_files.append(str(file_path))
            
            if not image_files:
                return None, [], []
            
            with sqlite3.connect(self.db_path) as conn:
                # 检查文件夹是否已存在
                cursor = conn.execute(
                    "SELECT id FROM folders WHERE path = ?", (folder_path,)
                )
                folder_result = cursor.fetchone()
                
                if not folder_result:
                    # 创建新文件夹记录
                    cursor = conn.execute(
                        "SELECT COALESCE(MAX(order_index), -1) FROM folders"
                    )
                    max_folder_order = cursor.fetchone()[0]
                    
                    cursor = conn.execute(
                        """
                        INSERT INTO folders (name, path, order_index)
                        VALUES (?, ?, ?)
                        """,
                        (folder_name, folder_path, max_folder_order + 1),
                    )
                    folder_id = cursor.lastrowid
                else:
                    folder_id = folder_result[0]
                
                # 获取已存在的图片路径
                cursor = conn.execute(
                    "SELECT path FROM images WHERE folder_id = ?", (folder_id,)
                )
                existing_images = {row[0] for row in cursor.fetchall()}
                
                # 过滤出新图片
                new_images = [f for f in image_files if f not in existing_images]
                
                if not new_images and folder_result:
                    return folder_id, [], list(existing_images)
                
                # 获取文件夹内所有图片（包括已存在和新增的）进行排序
                all_images = list(existing_images) + new_images
                sorted_images = sorted(all_images, key=get_sort_key_func)
                
                # 获取所有图片的排序位置映射
                position_map = {img: idx for idx, img in enumerate(sorted_images)}
                
                # 检查是否为手动排序文件夹
                cursor = conn.execute(
                    """
                    SELECT is_manual_sort FROM manual_sort_folders
                    WHERE folder_id = ?
                    """,
                    (folder_id,),
                )
                manual_sort_result = cursor.fetchone()
                is_manual_sort = manual_sort_result and manual_sort_result[0]
                
                if is_manual_sort:
                    # 手动排序文件夹：新图片添加到末尾，不改变现有顺序
                    cursor = conn.execute(
                        """
                        SELECT COALESCE(MAX(order_index), -1)
                        FROM images
                        WHERE folder_id = ?
                        """,
                        (folder_id,),
                    )
                    max_order = cursor.fetchone()[0]

                    # 🔧 优化：使用批量插入提升性能
                    batch_data = []
                    for i, file_path in enumerate(new_images):
                        name = Path(file_path).stem
                        order_index = max_order + 1 + i  # 添加到末尾
                        batch_data.append((name, file_path, folder_id, order_index))

                    # 批量插入
                    conn.executemany(
                        """
                        INSERT INTO images (name, path, folder_id, order_index)
                        VALUES (?, ?, ?, ?)
                        """,
                        batch_data,
                    )
                else:
                    # 自动排序文件夹：按文件名排序
                    # 🔧 优化：使用批量插入提升性能
                    batch_data = []
                    for file_path in new_images:
                        name = Path(file_path).stem
                        order_index = position_map[file_path]  # 使用排序后的位置
                        batch_data.append((name, file_path, folder_id, order_index))

                    # 批量插入新图片
                    conn.executemany(
                        """
                        INSERT INTO images (name, path, folder_id, order_index)
                        VALUES (?, ?, ?, ?)
                        """,
                        batch_data,
                    )
                    
                    # 🔧 优化：批量更新所有已存在图片的order_index以匹配新的排序
                    update_data = []
                    for file_path in existing_images:
                        order_index = position_map[file_path]
                        update_data.append((order_index, file_path, folder_id))

                    # 批量更新
                    if update_data:
                        conn.executemany(
                            """
                            UPDATE images
                            SET order_index = ?
                            WHERE path = ? AND folder_id = ?
                            """,
                            update_data,
                        )
                
                conn.commit()
                return folder_id, new_images, list(existing_images)
                
        except Exception as e:
            print(f"导入文件夹到数据库失败: {e}")
            raise e
    
    def sync_folders_in_db(self, folders: List[Tuple[int, str]], get_sort_key_func) -> Dict[str, int]:
        """
        同步文件夹数据库操作
        
        Args:
            folders: 文件夹列表 [(folder_id, folder_path), ...]
            get_sort_key_func: 排序键函数
            
        Returns:
            同步统计信息
        """
        stats = {
            "added": 0,
            "removed": 0,
            "updated": 0,
            "scanned_folders": 0,
            "scanned_files": 0,
        }
        
        try:
            # 使用多线程扫描文件夹
            def scan_folder(folder_info):
                """扫描单个文件夹的文件"""
                folder_id, folder_path = folder_info
                try:
                    folder_path_obj = Path(folder_path)
                    if not folder_path_obj.exists():
                        return folder_id, [], []
                    
                    # 获取文件夹中的所有图片文件
                    extensions = (".jpg", ".jpeg", ".png", ".bmp", ".gi", ".ti")
                    image_files = []
                    
                    for file_path in folder_path_obj.rglob("*"):
                        if (
                            file_path.is_file()
                            and file_path.suffix.lower() in extensions
                        ):
                            image_files.append(str(file_path))
                    
                    # 使用自定义排序函数对文件进行排序
                    image_files.sort(key=get_sort_key_func)
                    
                    return folder_id, image_files, []
                except Exception as e:
                    return folder_id, [], [str(e)]
            
            # 批量扫描文件夹
            folder_scan_results = {}
            with ThreadPoolExecutor(max_workers=4) as executor:
                future_to_folder = {
                    executor.submit(scan_folder, folder_info): folder_info
                    for folder_info in folders
                }
                
                for future in as_completed(future_to_folder):
                    folder_info = future_to_folder[future]
                    folder_id, image_files, errors = future.result()
                    folder_scan_results[folder_id] = (image_files, errors)
                    
                    # 更新统计
                    stats["scanned_folders"] += 1
                    stats["scanned_files"] += len(image_files)
                    
                    if errors:
                        print(f"扫描文件夹出错: {folder_info[1]}, 错误: {errors}")
            
            # 批量处理数据库操作
            with sqlite3.connect(self.db_path) as conn:
                batch_operations = []
                
                for folder_id, (image_files, errors) in folder_scan_results.items():
                    try:
                        # 检查是否为手动排序文件夹
                        cursor = conn.execute(
                            """
                            SELECT is_manual_sort FROM manual_sort_folders
                            WHERE folder_id = ?
                            """,
                            (folder_id,),
                        )
                        manual_sort_result = cursor.fetchone()
                        is_manual_sort = manual_sort_result and manual_sort_result[0]
                        
                        # 获取数据库中该文件夹的所有图片
                        cursor = conn.execute(
                            "SELECT id, path FROM images WHERE folder_id = ?",
                            (folder_id,),
                        )
                        db_images = {row[1]: row[0] for row in cursor.fetchall()}
                        
                        # 检查需要删除的图片
                        for db_path, img_id in db_images.items():
                            if not Path(db_path).exists():
                                batch_operations.append(
                                    {
                                        "query": "DELETE FROM images WHERE id = ?",
                                        "params": (img_id,),
                                    }
                                )
                                stats["removed"] += 1
                        
                        # 收集需要添加的新图片
                        new_images = []
                        for file_path in image_files:
                            if file_path not in db_images:
                                name = Path(file_path).stem
                                new_images.append((name, file_path))
                        
                        # 如果有新图片需要添加
                        if new_images:
                            if is_manual_sort:
                                # 手动排序文件夹：新图片添加到末尾
                                cursor = conn.execute(
                                    """
                                    SELECT COALESCE(MAX(order_index), -1)
                                    FROM images
                                    WHERE folder_id = ?
                                    """,
                                    (folder_id,),
                                )
                                max_order = cursor.fetchone()[0]
                                
                                for i, (name, file_path) in enumerate(new_images):
                                    batch_operations.append(
                                        {
                                            "query": """
                                                INSERT INTO images (name, path, folder_id, order_index)
                                                VALUES (?, ?, ?, ?)
                                            """,
                                            "params": (name, file_path, folder_id, max_order + 1 + i),
                                        }
                                    )
                                    stats["added"] += 1
                            else:
                                # 自动排序文件夹：按文件名排序，重新排序所有图片
                                all_images = list(db_images.keys()) + [fp for _, fp in new_images]
                                sorted_images = sorted(all_images, key=get_sort_key_func)
                                position_map = {img: idx for idx, img in enumerate(sorted_images)}

                                # 添加新图片
                                for name, file_path in new_images:
                                    order_index = position_map[file_path]
                                    batch_operations.append(
                                        {
                                            "query": """
                                                INSERT INTO images (name, path, folder_id, order_index)
                                                VALUES (?, ?, ?, ?)
                                            """,
                                            "params": (name, file_path, folder_id, order_index),
                                        }
                                    )
                                    stats["added"] += 1

                                # 更新现有图片的order_index
                                for db_path in db_images.keys():
                                    if Path(db_path).exists():  # 只更新仍存在的文件
                                        order_index = position_map[db_path]
                                        batch_operations.append(
                                            {
                                                "query": """
                                                    UPDATE images
                                                    SET order_index = ?
                                                    WHERE path = ? AND folder_id = ?
                                                """,
                                                "params": (order_index, db_path, folder_id),
                                            }
                                        )
                                        stats["updated"] += 1
                    
                    except Exception as e:
                        print(f"处理文件夹 {folder_id} 时出错: {e}")
                
                # 执行批量操作
                for operation in batch_operations:
                    conn.execute(operation["query"], operation["params"])
                
                conn.commit()
                
            return stats
            
        except Exception as e:
            print(f"同步文件夹数据库操作失败: {e}")
            raise e
    
    def check_standalone_images(self) -> List[Tuple[int, str]]:
        """检查单独的图片文件"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    "SELECT id, path FROM images WHERE folder_id IS NULL"
                )
                return cursor.fetchall()
        except Exception as e:
            print(f"检查单独图片失败: {e}")
            return []
