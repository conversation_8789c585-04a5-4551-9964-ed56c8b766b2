#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPU加速滚动引擎 - gpu_scroll_engine.py
专为歌谱投影控制器设计的高性能滚动渲染系统

使用ModernGL + 您的AMD Radeon显卡实现：
- 硬件加速图片渲染
- VSYNC防撕裂
- 60fps+流畅滚动
- 双屏同步投影
- 低延迟响应
"""

import moderngl
import pygame
import numpy as np
import time
import threading
from PIL import Image
from pathlib import Path
import tkinter as tk

class GPUScrollEngine:
    """GPU加速滚动引擎"""
    
    def __init__(self, width, height, vsync=True):
        """初始化GPU滚动引擎
        
        Args:
            width: 渲染宽度
            height: 渲染高度  
            vsync: 是否启用垂直同步（防撕裂）
        """
        self.width = width
        self.height = height
        self.vsync = vsync
        
        # 初始化Pygame（隐藏窗口模式）
        pygame.init()
        pygame.display.set_mode((1, 1), pygame.OPENGL | pygame.HIDDEN)
        
        # 创建ModernGL上下文
        self.ctx = moderngl.create_context()
        
        # 设置VSYNC
        if vsync:
            pygame.display.gl_set_attribute(pygame.GL_SWAP_CONTROL, 1)
        
        self._setup_shaders()
        self._setup_buffers()
        
        # 纹理缓存
        self.texture_cache = {}
        self.max_cache_size = 50
        
        # 动画状态
        self.current_scroll_pos = 0.0
        self.target_scroll_pos = 0.0
        self.is_animating = False
        self.animation_start_time = 0
        self.animation_duration = 1.0
        
        # 性能统计
        self.frame_count = 0
        self.fps_start_time = time.time()
        self.current_fps = 0
        
    def _setup_shaders(self):
        """设置GPU着色器程序"""
        
        # 顶点着色器（处理图片位置变换）
        vertex_shader = """
        #version 330 core
        
        in vec2 in_position;
        in vec2 in_texcoord;
        
        uniform mat4 projection;
        uniform float scroll_offset;
        uniform float image_height_ratio;
        
        out vec2 texcoord;
        
        void main() {
            vec2 pos = in_position;
            
            // 应用滚动偏移
            pos.y += scroll_offset;
            
            gl_Position = projection * vec4(pos, 0.0, 1.0);
            texcoord = in_texcoord;
        }
        """
        
        # 片段着色器（处理图片渲染和效果）
        fragment_shader = """
        #version 330 core
        
        in vec2 texcoord;
        out vec4 fragColor;
        
        uniform sampler2D image_texture;
        uniform bool invert_colors;
        uniform bool yellow_text_mode;
        uniform vec3 yellow_color;
        uniform float alpha;
        
        void main() {
            vec4 color = texture(image_texture, texcoord);
            
            if (yellow_text_mode) {
                // 黄字效果：背景变黑，文字变黄
                float brightness = (color.r + color.g + color.b) / 3.0;
                if (brightness > 0.5) {
                    // 亮区域变成指定黄色
                    color.rgb = yellow_color;
                } else {
                    // 暗区域变成黑色
                    color.rgb = vec3(0.0, 0.0, 0.0);
                }
            } else if (invert_colors) {
                // 简单反色
                color.rgb = vec3(1.0) - color.rgb;
            }
            
            color.a *= alpha;
            fragColor = color;
        }
        """
        
        # 编译着色器程序
        self.program = self.ctx.program(
            vertex_shader=vertex_shader,
            fragment_shader=fragment_shader
        )
        
    def _setup_buffers(self):
        """设置GPU缓冲区"""
        
        # 创建正交投影矩阵
        self.projection_matrix = self._create_projection_matrix()
        
        # 创建顶点缓冲区（全屏四边形）
        vertices = np.array([
            # 位置        纹理坐标
            -1.0, -1.0,   0.0, 1.0,
             1.0, -1.0,   1.0, 1.0,
             1.0,  1.0,   1.0, 0.0,
            -1.0,  1.0,   0.0, 0.0,
        ], dtype=np.float32)
        
        indices = np.array([0, 1, 2, 2, 3, 0], dtype=np.uint32)
        
        # 创建缓冲区对象
        self.vbo = self.ctx.buffer(vertices.tobytes())
        self.ibo = self.ctx.buffer(indices.tobytes())
        
        # 创建顶点数组对象
        self.vao = self.ctx.vertex_array(
            self.program,
            [(self.vbo, '2f 2f', 'in_position', 'in_texcoord')],
            self.ibo
        )
        
    def _create_projection_matrix(self):
        """创建正交投影矩阵"""
        # 创建4x4正交投影矩阵（用于2D渲染）
        left = -1.0
        right = 1.0
        bottom = -1.0
        top = 1.0
        near = -1.0
        far = 1.0
        
        # 标准正交投影矩阵
        matrix = np.array([
            [2.0/(right-left), 0, 0, -(right+left)/(right-left)],
            [0, 2.0/(top-bottom), 0, -(top+bottom)/(top-bottom)],
            [0, 0, -2.0/(far-near), -(far+near)/(far-near)],
            [0, 0, 0, 1]
        ], dtype=np.float32)
        
        return matrix.flatten()  # 返回扁平化的数组
        
    def load_image_texture(self, image_path):
        """加载图片为GPU纹理
        
        Args:
            image_path: 图片文件路径
            
        Returns:
            ModernGL纹理对象
        """
        image_path = str(image_path)
        
        # 检查缓存
        if image_path in self.texture_cache:
            return self.texture_cache[image_path]
            
        try:
            # 使用PIL加载图片
            with Image.open(image_path) as img:
                # 转换为RGBA格式
                img = img.convert('RGBA')
                
                # 检查尺寸限制
                max_size = self.ctx.info['GL_MAX_TEXTURE_SIZE']
                if img.width > max_size or img.height > max_size:
                    # 等比缩放到最大尺寸
                    ratio = min(max_size / img.width, max_size / img.height)
                    new_size = (int(img.width * ratio), int(img.height * ratio))
                    img = img.resize(new_size, Image.Resampling.LANCZOS)
                    
                # 创建GPU纹理
                texture = self.ctx.texture(img.size, 4, img.tobytes())
                
                # 设置纹理参数
                texture.filter = (moderngl.LINEAR, moderngl.LINEAR)
                # 兼容不同版本的ModernGL常量
                try:
                    clamp_mode = moderngl.CLAMP_TO_EDGE
                except AttributeError:
                    # 旧版本使用数字常量
                    clamp_mode = 0x812F  # GL_CLAMP_TO_EDGE的值
                texture.wrap_x = clamp_mode
                texture.wrap_y = clamp_mode
                
                # 加入缓存
                if len(self.texture_cache) >= self.max_cache_size:
                    # 清理最老的纹理
                    oldest_key = next(iter(self.texture_cache))
                    old_texture = self.texture_cache.pop(oldest_key)
                    old_texture.release()
                    
                self.texture_cache[image_path] = texture
                
                return texture
                
        except Exception as e:
            return None
            
    def smooth_scroll_to(self, target_position, duration=1.0, easing='ease_out'):
        """GPU加速平滑滚动
        
        Args:
            target_position: 目标滚动位置 (0.0-1.0)
            duration: 动画时长（秒）
            easing: 缓动函数类型
        """
        self.target_scroll_pos = max(0.0, min(1.0, target_position))
        self.animation_duration = duration
        self.animation_start_time = time.time()
        self.is_animating = True
        self.easing_function = self._get_easing_function(easing)
        
    def _get_easing_function(self, easing_type):
        """获取缓动函数"""
        easing_functions = {
            'linear': lambda t: t,
            'ease_in': lambda t: t * t,
            'ease_out': lambda t: 1 - (1 - t) * (1 - t),
            'ease_in_out': lambda t: 3*t*t - 2*t*t*t,
            'elastic': lambda t: (2**(-10*t)) * np.sin((t-0.1)*5*np.pi) + 1 if t > 0 else 0
        }
        return easing_functions.get(easing_type, easing_functions['ease_out'])
        
    def update_animation(self):
        """更新动画状态"""
        if not self.is_animating:
            return False
            
        current_time = time.time()
        elapsed = current_time - self.animation_start_time
        
        if elapsed >= self.animation_duration:
            # 动画完成
            self.current_scroll_pos = self.target_scroll_pos
            self.is_animating = False
            return False
        else:
            # 计算当前位置
            progress = elapsed / self.animation_duration
            eased_progress = self.easing_function(progress)
            
            start_pos = self.current_scroll_pos
            self.current_scroll_pos = start_pos + (self.target_scroll_pos - start_pos) * eased_progress
            return True
            
    def render_frame(self, texture, 
                    invert_colors=False, 
                    yellow_text_mode=False,
                    yellow_color=(1.0, 1.0, 0.0),
                    alpha=1.0):
        """渲染一帧
        
        Args:
            texture: GPU纹理对象
            invert_colors: 是否反色
            yellow_text_mode: 是否黄字模式
            yellow_color: 黄字颜色RGB
            alpha: 透明度
        """
        if texture is None:
            return
            
        # 更新动画
        self.update_animation()
        
        # 清空画布
        self.ctx.clear(0.0, 0.0, 0.0, 1.0)
        
        # 绑定纹理
        texture.use(0)
        
        # 设置着色器参数
        self.program['projection'].write(self.projection_matrix.tobytes())
        self.program['scroll_offset'].value = self.current_scroll_pos
        self.program['image_texture'].value = 0
        self.program['invert_colors'].value = invert_colors
        self.program['yellow_text_mode'].value = yellow_text_mode
        self.program['yellow_color'].value = yellow_color
        self.program['alpha'].value = alpha
        
        # 渲染
        self.vao.render()
        
        # 更新FPS统计
        self._update_fps_counter()
        
    def _update_fps_counter(self):
        """更新FPS计数器"""
        self.frame_count += 1
        current_time = time.time()
        
        if current_time - self.fps_start_time >= 1.0:
            self.current_fps = self.frame_count / (current_time - self.fps_start_time)
            self.frame_count = 0
            self.fps_start_time = current_time
            
    def get_performance_info(self):
        """获取性能信息"""
        return {
            'fps': self.current_fps,
            'gpu_vendor': self.ctx.info['GL_VENDOR'],
            'gpu_renderer': self.ctx.info['GL_RENDERER'],
            'opengl_version': self.ctx.info['GL_VERSION'],
            'texture_cache_size': len(self.texture_cache),
            'is_animating': self.is_animating,
            'current_scroll_pos': self.current_scroll_pos
        }
        
    def cleanup(self):
        """清理GPU资源"""
        try:
            # 清理纹理缓存
            for texture in self.texture_cache.values():
                texture.release()
            self.texture_cache.clear()
            
            # 清理GPU对象
            self.vao.release()
            self.vbo.release()
            self.ibo.release()
            self.program.release()
            
            pygame.quit()
            
        except Exception as e:
            pass


class TkinterGPUBridge:
    """Tkinter与GPU渲染引擎的桥接器"""
    
    def __init__(self, canvas, projection_manager=None):
        """初始化桥接器
        
        Args:
            canvas: Tkinter Canvas对象
            projection_manager: 投影管理器（可选）
        """
        self.canvas = canvas
        self.projection_manager = projection_manager
        
        # 获取canvas尺寸
        self.canvas.update_idletasks()
        width = self.canvas.winfo_width()
        height = self.canvas.winfo_height()
        
        # 初始化GPU引擎
        self.gpu_engine = GPUScrollEngine(width, height, vsync=True)
        
        # 当前图片纹理
        self.current_texture = None
        self.current_image_path = None
        
    def load_image(self, image_path):
        """加载图片到GPU"""
        try:
            if str(image_path) != self.current_image_path:
                self.current_texture = self.gpu_engine.load_image_texture(image_path)
                self.current_image_path = str(image_path)
                
            return self.current_texture is not None
            
        except Exception as e:
            return False
            
    def smooth_scroll_to(self, target_position, duration=1.0):
        """触发GPU平滑滚动"""
        if self.current_texture:
            self.gpu_engine.smooth_scroll_to(target_position, duration)
            
            # 启动渲染循环
            self._start_render_loop()
            
    def _start_render_loop(self):
        """启动GPU渲染循环"""
        def render_loop():
            if self.gpu_engine.is_animating and self.current_texture:
                # GPU渲染
                self.gpu_engine.render_frame(
                    self.current_texture,
                    invert_colors=getattr(self, 'invert_colors', False),
                    yellow_text_mode=getattr(self, 'yellow_text_mode', False),
                    yellow_color=getattr(self, 'yellow_color', (1.0, 1.0, 0.0))
                )
                
                # 同步投影屏幕
                if self.projection_manager:
                    self.projection_manager.sync_with_gpu(self.gpu_engine)
                
                # 继续渲染
                self.canvas.after(16, render_loop)  # ~60fps
                
        render_loop()
        
    def get_performance_info(self):
        """获取性能统计"""
        return self.gpu_engine.get_performance_info()
        
    def cleanup(self):
        """清理资源"""
        self.gpu_engine.cleanup()


# 使用示例和测试代码
def test_gpu_scroll_engine():
    """测试GPU滚动引擎"""
    print("🧪 GPU滚动引擎测试开始...")
    
    try:
        # 创建测试引擎
        engine = GPUScrollEngine(800, 600)
        
        # 显示性能信息
        info = engine.get_performance_info()
        print(f"GPU信息: {info['gpu_renderer']}")
        print(f"OpenGL版本: {info['opengl_version']}")
        
        # 测试完成
        engine.cleanup()
        print("✅ GPU滚动引擎测试通过!")
        
    except Exception as e:
        print(f"❌ GPU滚动引擎测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_gpu_scroll_engine()
